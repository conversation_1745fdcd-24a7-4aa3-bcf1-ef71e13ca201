# BigRec_FELLRec 数据目录详细说明

## 📁 数据目录结构概览

```
BigRec_FELLRec/data/
├── 📁 games/                          # 游戏推荐数据集
│   ├── 📄 train*.json                 # 训练数据文件
│   ├── 📄 valid*.json                 # 验证数据文件
│   ├── 📄 test*.json                  # 测试数据文件
│   ├── 📄 meta_data.json              # 物品元数据
│   ├── 📄 *.pt                        # 预训练嵌入文件
│   ├── 📄 evaluate.py                 # 评估脚本
│   ├── 📄 evaluate.sh                 # 评估启动脚本
│   └── 📄 utils.py                    # 评估工具函数
├── 📄 train_client_data.pkl           # 客户端训练数据
├── 📄 valid_client_data.pkl           # 客户端验证数据
└── 📄 test_client_data.pkl            # 客户端测试数据
```

## 📊 数据文件详细说明

### 1. 训练数据文件

#### train_1024_user.json
- **用途**：包含1024个用户的训练数据
- **格式**：JSON格式，每行一个训练样本
- **数据结构**：
```json
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说》、《超级马里奥》、《动物森友会》",
    "output": "推荐：《马里奥卡丁车》",
    "user": 123
}
```

#### train_2048_user.json / train_4096_user.json
- **用途**：包含更多用户的训练数据（2048/4096个用户）
- **特点**：数据量更大，适合大规模实验

#### train_interactions.json
- **用途**：原始用户-物品交互数据
- **格式**：用户ID、物品ID、评分、时间戳等

### 2. 验证数据文件

#### valid_5000_user.json
- **用途**：包含5000个用户的验证数据
- **作用**：模型训练过程中的性能监控
- **特点**：与训练数据格式相同，但用于评估

#### valid_interactions.json
- **用途**：原始验证交互数据
- **作用**：用于计算传统推荐指标

### 3. 测试数据文件

#### test_user.json
- **用途**：最终模型评估的测试数据
- **重要性**：⭐⭐⭐ 用于论文结果报告
- **特点**：不参与训练，仅用于最终评估

#### test_interactions.json
- **用途**：原始测试交互数据
- **作用**：计算最终的推荐性能指标

### 4. 元数据文件

#### meta_data.json
- **用途**：游戏物品的详细信息
- **内容**：游戏标题、类型、描述、发布日期等
- **数据结构**：
```json
{
    "game_id_123": {
        "title": "塞尔达传说：旷野之息",
        "genre": ["动作", "冒险"],
        "description": "开放世界冒险游戏...",
        "release_date": "2017-03-03"
    }
}
```

### 5. 预训练嵌入文件

#### movie_embeddings.pt
- **用途**：预训练的物品嵌入向量
- **格式**：PyTorch张量文件
- **维度**：[物品数量, 嵌入维度]

#### predict_embeddings.pt
- **用途**：预测阶段使用的嵌入向量
- **作用**：加速推理过程

## 🔄 数据处理流程

### 数据预处理流程
```
原始交互数据 → 格式转换 → 文本生成 → 分词处理 → 联邦分割
```

### 详细处理步骤

#### 步骤1：原始数据加载
```python
# 加载交互数据
interactions = pd.read_json('train_interactions.json')

# 加载元数据
metadata = json.load(open('meta_data.json'))
```

#### 步骤2：文本格式转换
```python
def create_prompt(user_history, target_item):
    """将交互数据转换为自然语言提示"""
    history_text = "、".join([metadata[item]['title'] for item in user_history])
    target_text = metadata[target_item]['title']
    
    return {
        "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
        "input": f"用户玩过：{history_text}",
        "output": f"推荐：{target_text}",
        "user": user_id
    }
```

#### 步骤3：联邦数据分割
```python
# 基于用户嵌入进行聚类分割
user_embeddings = load_user_embeddings()
client_assignment = kmeans_clustering(user_embeddings, n_clients=5)

# 生成客户端数据文件
for client_id in range(n_clients):
    client_data = filter_by_client(train_data, client_assignment, client_id)
    save_pickle(client_data, f'train_client_data.pkl')
```

## 📈 数据统计信息

### 数据集规模
- **用户数量**：约10,000个活跃用户
- **物品数量**：约50,000个游戏
- **交互数量**：约1,000,000条用户-游戏交互
- **平均用户交互**：每用户约100个游戏
- **数据稀疏度**：约99.8%（典型推荐系统特征）

### 数据分布
```python
# 用户活跃度分布
用户交互数量分布：
- 1-10个游戏：30%的用户
- 11-50个游戏：40%的用户  
- 51-100个游戏：20%的用户
- 100+个游戏：10%的用户

# 游戏流行度分布
游戏被交互次数分布：
- 长尾分布：少数热门游戏占大部分交互
- 头部20%游戏：占80%交互
- 尾部80%游戏：占20%交互
```

## 🛠️ 评估脚本说明

### evaluate.py - 主评估脚本
**功能**：
- 加载推理结果文件
- 计算推荐准确率指标
- 生成评估报告

**使用方法**：
```bash
cd BigRec_FELLRec/data/games
python evaluate.py --input_dir "../../推理结果目录"
```

### evaluate.sh - 评估启动脚本
**功能**：
- 批量评估多个模型
- 自动化评估流程

### utils.py - 评估工具函数
**主要函数**：
- `computeTopNAccuracy()`: 计算Top-N准确率
- `print_results()`: 格式化输出结果
- `calculate_ndcg()`: 计算NDCG指标

## 📋 数据质量检查

### 数据完整性检查
```python
def check_data_integrity():
    """检查数据文件的完整性"""
    required_files = [
        'train_1024_user.json',
        'valid_5000_user.json', 
        'test_user.json',
        'meta_data.json'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺失文件: {file}")
        else:
            print(f"✅ 文件存在: {file}")
```

### 数据格式验证
```python
def validate_data_format(json_file):
    """验证JSON数据格式"""
    required_keys = ['instruction', 'input', 'output', 'user']
    
    with open(json_file, 'r') as f:
        for line_num, line in enumerate(f):
            data = json.loads(line)
            for key in required_keys:
                if key not in data:
                    print(f"❌ 第{line_num}行缺失字段: {key}")
```

## 🔧 数据预处理工具

### 数据转换脚本
```python
def convert_interactions_to_prompts(interactions_file, output_file):
    """将交互数据转换为训练提示格式"""
    interactions = load_interactions(interactions_file)
    prompts = []
    
    for user_id, user_data in interactions.groupby('user_id'):
        # 生成用户历史序列
        history = user_data['item_id'].tolist()[:-1]
        target = user_data['item_id'].tolist()[-1]
        
        # 创建训练提示
        prompt = create_training_prompt(history, target, user_id)
        prompts.append(prompt)
    
    # 保存结果
    save_json(prompts, output_file)
```

### 数据增强工具
```python
def augment_training_data(original_data, augment_ratio=0.2):
    """数据增强：生成更多训练样本"""
    augmented_data = []
    
    for sample in original_data:
        # 原始样本
        augmented_data.append(sample)
        
        # 生成增强样本
        if random.random() < augment_ratio:
            augmented_sample = create_augmented_sample(sample)
            augmented_data.append(augmented_sample)
    
    return augmented_data
```

## 📊 数据使用最佳实践

### 1. 数据加载优化
```python
# 使用生成器避免内存溢出
def load_large_dataset(file_path):
    with open(file_path, 'r') as f:
        for line in f:
            yield json.loads(line)

# 批量处理
def process_in_batches(dataset, batch_size=1000):
    batch = []
    for item in dataset:
        batch.append(item)
        if len(batch) >= batch_size:
            yield batch
            batch = []
    if batch:
        yield batch
```

### 2. 内存管理
```python
# 及时释放不需要的数据
del large_dataset
torch.cuda.empty_cache()

# 使用数据流水线
dataset = dataset.map(preprocess_function)
dataset = dataset.batch(batch_size)
dataset = dataset.prefetch(buffer_size=2)
```

### 3. 数据验证
```python
# 训练前验证数据
def validate_before_training(train_data, val_data, test_data):
    """训练前的数据验证"""
    # 检查数据重叠
    check_data_overlap(train_data, test_data)
    
    # 检查数据分布
    check_data_distribution(train_data, val_data)
    
    # 检查格式一致性
    check_format_consistency(train_data, val_data, test_data)
```

这个详细的数据说明文档帮助您完全理解FELLRec项目的数据组织和使用方式！
