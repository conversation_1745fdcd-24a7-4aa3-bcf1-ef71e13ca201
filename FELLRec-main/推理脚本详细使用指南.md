# FELLRec推理脚本详细使用指南

## 📖 脚本概述

`inference.py` 是FELLRec联邦学习推荐系统的推理模块，负责使用训练好的模型生成个性化推荐结果。

### 🎯 主要功能
1. **多客户端模型加载**：自动加载每个客户端的最佳训练模型
2. **批量推理处理**：高效处理大量测试数据
3. **推荐结果生成**：将用户历史转换为个性化推荐
4. **结果保存管理**：自动保存和合并推理结果

## 🔄 推理流程详解

### 整体流程图
```
客户端数据加载 → 模型初始化 → 批量推理 → 结果保存 → 下一客户端
     ↓              ↓            ↓          ↓
  test_data.pkl → LLaMA+LoRA → 推荐生成 → JSON文件
```

### 详细步骤说明

#### 步骤1：环境初始化
```python
# 设备检测和配置
if torch.cuda.is_available():
    device = "cuda"  # 优先使用GPU
else:
    device = "cpu"   # 备选CPU
```

**关键点**：
- 自动检测可用的计算设备
- 支持CUDA、MPS、CPU三种设备
- 优化线程配置避免资源竞争

#### 步骤2：客户端数据加载
```python
with open('./data/test_client_data.pkl', 'rb') as file:
    test_data_all = pickle.load(file)
```

**数据结构**：
```python
test_data_all = [
    [  # 客户端0的测试数据
        {
            "instruction": "根据用户历史推荐游戏",
            "input": "用户玩过：游戏A、游戏B",
            "output": "推荐：游戏C",
            "user": 123
        },
        # ... 更多测试样本
    ],
    [  # 客户端1的测试数据
        # ... 客户端1的测试样本
    ],
    # ... 更多客户端
]
```

#### 步骤3：模型加载循环
```python
for client_idx in range(len(test_data_all)):
    # 构建客户端模型路径
    lora_weights = f'./model/games/1_1024/best_client{client_idx}_model'
    
    # 加载基础模型 + LoRA权重
    model = LlamaForCausalLM.from_pretrained(base_model)
    model = PeftModel.from_pretrained(model, lora_weights)
```

**关键技术**：
- **基础模型**：LLaMA提供语言理解能力
- **LoRA权重**：客户端特定的推荐知识
- **PEFT框架**：高效加载和管理LoRA权重

#### 步骤4：批量推理处理
```python
def evaluate(instructions, inputs, **kwargs):
    # 构建提示 → 分词 → 生成 → 解码
    prompt = [generate_prompt(inst, inp) for inst, inp in zip(instructions, inputs)]
    inputs = tokenizer(prompt, ...)
    outputs = model.generate(**inputs, ...)
    return decoded_outputs
```

**推理参数说明**：
- `temperature=0`: 确定性生成，保证推荐一致性
- `top_p=0.9`: 核采样，平衡质量和多样性
- `num_beams=4`: 束搜索，提高生成质量
- `max_new_tokens=128`: 限制推荐长度

#### 步骤5：结果保存和合并
```python
# 为每个测试样本添加预测结果
for i, test in enumerate(test_data):
    test_data[i]['predict'] = outputs[i]

# 保存到JSON文件
with open(f'games_client{client_idx}.json', 'w') as f:
    json.dump(result, f, indent=4)
```

## 🛠️ 使用方法

### 基本使用
```bash
# 最简单的调用方式
python inference.py --base_model "path/to/llama-7b"
```

### 完整参数调用
```bash
python inference.py \
    --base_model "path/to/llama-7b-hf" \
    --load_8bit False \
    --batch_size 4 \
    --test_data_path "data/games/test.json" \
    --result_json_data "my_results.json"
```

### 参数详细说明

#### 必需参数
- `--base_model`: LLaMA基础模型路径
  ```bash
  --base_model "/path/to/llama-7b-hf"
  ```

#### 可选参数
- `--load_8bit`: 是否使用8位量化（默认False）
  ```bash
  --load_8bit True  # 节省显存但可能影响质量
  ```

- `--batch_size`: 批处理大小（默认1）
  ```bash
  --batch_size 4    # 根据显存大小调整
  ```

## 📊 输入输出格式

### 输入数据格式
```json
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说：旷野之息》、《超级马里奥：奥德赛》、《动物森友会》",
    "output": "推荐：《马里奥卡丁车8豪华版》",
    "user": 12345
}
```

### 输出结果格式
```json
{
    "instruction": "根据用户的游戏历史，推荐下一个可能喜欢的游戏",
    "input": "用户玩过：《塞尔达传说：旷野之息》、《超级马里奥：奥德赛》、《动物森友会》",
    "output": "推荐：《马里奥卡丁车8豪华版》",
    "predict": "推荐：《马里奥卡丁车8豪华版》、《塞尔达传说：王国之泪》",
    "user": 12345
}
```

### 字段说明
- `instruction`: 推荐任务描述
- `input`: 用户历史行为
- `output`: 真实推荐结果（用于评估）
- `predict`: 模型预测结果（新增字段）
- `user`: 用户ID

## ⚙️ 性能优化

### 显存优化
```python
# 1. 使用8位量化
--load_8bit True

# 2. 减少批次大小
--batch_size 1

# 3. 启用梯度检查点
model.gradient_checkpointing_enable()
```

### 速度优化
```python
# 1. 增加批次大小
--batch_size 8

# 2. 使用模型编译（PyTorch 2.0+）
model = torch.compile(model)

# 3. 使用半精度
torch_dtype=torch.float16
```

### 内存管理
```python
# 每个客户端处理完后清理内存
del model
torch.cuda.empty_cache()
```

## 🔍 调试和监控

### 进度监控
```python
# 使用tqdm显示推理进度
for i, batch_ in tqdm(enumerate(batches), desc="推理进度"):
    # 推理处理
```

### 错误处理
```python
try:
    # 推理逻辑
    outputs = evaluate(instructions, inputs)
except Exception as e:
    print(f"推理出错: {e}")
    import ipdb; ipdb.set_trace()  # 启动调试器
```

### 日志输出
```
🚀 检测到CUDA设备，将使用GPU加速推理
📂 正在加载客户端测试数据...
📊 成功加载 5 个客户端的测试数据
🔄 开始处理客户端 1/5
📁 客户端 0 模型路径: ./model/games/1_1024/best_client0_model
🤖 正在加载客户端 0 的模型...
✅ 客户端 0 推理完成，共生成 1000 个结果
```

## 🚨 常见问题和解决方案

### 1. 显存不足
```
错误：CUDA out of memory
解决方案：
- 减少batch_size: 4 → 2 → 1
- 启用8位量化: --load_8bit True
- 使用CPU推理: CUDA_VISIBLE_DEVICES=""
```

### 2. 模型加载失败
```
错误：Can't load model
解决方案：
- 检查base_model路径是否正确
- 确认LoRA权重文件存在
- 验证模型格式兼容性
```

### 3. 推理结果异常
```
问题：生成的推荐不合理
解决方案：
- 检查temperature参数（推荐0-0.1）
- 验证输入数据格式
- 调整生成参数（top_p, top_k）
```

### 4. 速度过慢
```
问题：推理速度太慢
解决方案：
- 增加batch_size（在显存允许范围内）
- 启用模型编译（PyTorch 2.0+）
- 使用更强的GPU设备
```

## 📈 结果分析

### 推理质量评估
```python
# 计算推荐准确率
def calculate_accuracy(predictions, ground_truth):
    correct = 0
    for pred, truth in zip(predictions, ground_truth):
        if pred.strip() == truth.strip():
            correct += 1
    return correct / len(predictions)
```

### 推理效率统计
```python
import time

start_time = time.time()
# 执行推理
end_time = time.time()

print(f"推理耗时: {end_time - start_time:.2f}秒")
print(f"平均每样本: {(end_time - start_time) / len(test_data):.3f}秒")
```

## 🎯 最佳实践

1. **预处理检查**：推理前验证数据格式和模型路径
2. **批量处理**：根据硬件配置选择合适的batch_size
3. **内存管理**：及时清理不需要的模型和变量
4. **结果验证**：检查生成结果的合理性和格式
5. **错误处理**：添加异常捕获和调试断点

这个推理脚本是FELLRec系统的重要组成部分，正确使用它可以高效地生成高质量的个性化推荐结果！
