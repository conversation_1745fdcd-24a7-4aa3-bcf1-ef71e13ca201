"""
FELLRec推荐系统评估脚本

==============================================================================
文件功能描述：
==============================================================================
本脚本用于评估训练好的联邦学习推荐模型的性能。主要功能包括：
1. 加载训练好的推荐模型
2. 对测试数据进行推理
3. 计算推荐准确率等评估指标
4. 生成详细的评估报告

==============================================================================
评估指标说明：
==============================================================================
- Top-K准确率：推荐的前K个物品中包含真实物品的比例
- Hit Rate：命中率，衡量推荐系统的覆盖能力
- NDCG：归一化折损累积增益，考虑推荐位置的重要性

==============================================================================
使用方法：
==============================================================================
python evaluate.py --input_dir "path/to/prediction/files"

参数说明：
- input_dir: 包含推理结果文件的目录路径
==============================================================================
"""

# 核心库导入
from transformers import GenerationConfig, LlamaForCausalLM, LlamaTokenizer
import transformers
import numpy as np
import torch
import os
import math
import json
import torch
import ipdb  # 调试工具
import argparse
import pickle

# 自定义评估工具
from utils import computeTopNAccuracy, print_results

# ==================== 命令行参数解析 ====================
parse = argparse.ArgumentParser(description='FELLRec推荐系统评估脚本')
parse.add_argument("--input_dir", type=str, default="./",
                  help="推理结果文件所在目录路径")
args = parse.parse_args()

# ==================== 文件路径收集 ====================
print("🔍 正在扫描推理结果文件...")
path = []
for root, dirs, files in os.walk(args.input_dir):
    for name in files:
        if name.endswith('.json'):  # 只处理JSON格式的推理结果
            path.append(os.path.join(args.input_dir, name))

print(f"📁 找到 {len(path)} 个推理结果文件")

# ==================== 设备配置 ====================
# 指定使用的GPU设备
os.environ["CUDA_VISIBLE_DEVICES"] = "4,5"

# 自动检测可用设备
if torch.cuda.is_available():
    device = "cuda"
    print(f"🚀 使用GPU设备: {torch.cuda.get_device_name()}")
else:
    device = "cpu"
    print("💻 使用CPU设备")

# 检查Apple Silicon的MPS支持
try:
    if torch.backends.mps.is_available():
        device = "mps"
        print("🍎 使用Apple MPS设备")
except:  # noqa: E722
    pass

base_model = " "
tokenizer = LlamaTokenizer.from_pretrained(base_model)
model = LlamaForCausalLM.from_pretrained(
    base_model,
    torch_dtype=torch.float16,
    device_map="auto",
)

model.half()  # seems to fix bugs for some users.

model.config.pad_token_id = tokenizer.pad_token_id = 0  # unk
model.config.bos_token_id = 1
model.config.eos_token_id = 2


movies = np.load('../../../data/games/id_to_title_map.npy', allow_pickle=True).item()
movie_dict = {value.strip(" "): key for key, value in movies.items()}
movie_names = list(movies.values())

tokenizer.padding_side = "left"
def batch(list, batch_size=1):
    chunk_size = (len(list) - 1) // batch_size + 1
    for i in range(chunk_size):
        yield list[batch_size * i: batch_size * (i + 1)]

movie_embeddings = []
from tqdm import tqdm

model.eval()
for i, id in tqdm(enumerate(batch(torch.arange(len(movie_names)), 4))):
    name = [movie_names[_] for _ in id]
    input = tokenizer(name, return_tensors="pt", padding=True).to(device)
    input_ids = input.input_ids
    attention_mask = input.attention_mask
    outputs = model(input_ids, attention_mask=attention_mask, output_hidden_states=True)
    hidden_states = outputs.hidden_states
    movie_embeddings.append(hidden_states[-1][:, -1, :].detach().cpu())
movie_embeddings = torch.cat(movie_embeddings, dim=0).cuda()

# save movie_embeddings
torch.save(movie_embeddings, './movie_embeddings.pt')

# load test_client_data.pkl
with open('../test_client_data.pkl', 'rb') as file:
    test_client_data = pickle.load(file)
path = ['../../games_client4.json']
f = open(path[0], 'r')
import json
test_data_all = json.load(f)
f.close()

test_num = [len(client) for client in test_client_data]
client_result = []
for cnt, client in enumerate(test_client_data):
    if cnt == 0:
        test_data = test_data_all[:len(client)]
    else:
        begin = 0
        for i in range(cnt):
            begin += len(test_client_data[i]) 
        end = begin + len(client)
        test_data = test_data_all[begin:end]   
    model.config.pad_token_id = tokenizer.pad_token_id = 0  # unk
    model.config.bos_token_id = 1
    model.config.eos_token_id = 2
    model.eval()
    text = [_["predict"].strip("\"") for _ in test_data]
    # text = [_["output"].strip("\"") for _ in test_data]
    tokenizer.padding_side = "left"

    def batch(list, batch_size=1):
        chunk_size = (len(list) - 1) // batch_size + 1
        for i in range(chunk_size):
            yield list[batch_size * i: batch_size * (i + 1)]
    predict_embeddings = []
    from tqdm import tqdm
    for i, batch_input in tqdm(enumerate(batch(text, 4))):
        input = tokenizer(batch_input, return_tensors="pt", padding=True).to(device)
        input_ids = input.input_ids
        attention_mask = input.attention_mask
        outputs = model(input_ids, attention_mask=attention_mask, output_hidden_states=True)
        hidden_states = outputs.hidden_states
        predict_embeddings.append(hidden_states[-1][:, -1, :].detach().cpu())
    
    predict_embeddings = torch.cat(predict_embeddings, dim=0).cuda()
    torch.save(predict_embeddings, './predict_embeddings.pt')
    

    dist = torch.cdist(predict_embeddings, movie_embeddings, p=2)
    dist = dist.float()
    ground_truth = []


    # load train_dict.npy
    train_dict = np.load('../../../data/games/training_dict.npy', allow_pickle=True).item()
    valid_dict = np.load('../../../data/games/validation_dict.npy', allow_pickle=True).item()
    # for every user
    for i in range(len(test_data)):
        user_gt = []
        parts = test_data[i]['output'].split('\", \"')
        # print(parts)
        target_item = [part.strip(" ") for part in parts if part.strip(" ") != '' and part.strip(" ") != ',']
        # print(target_item)
        for cnt, item in enumerate(target_item):
            if cnt == 0:
                item = item[1:] 
            if cnt == len(target_item) - 1:
                item = item[:-1]
            item = item.strip(" ")
            _ = movie_dict[item]
            user_gt.append(_)
        user = test_data[i]['user']
        if user in valid_dict and user in train_dict:
            history_item = train_dict[user] + valid_dict[user]
        elif user in valid_dict:
            history_item = valid_dict[user]
        elif user in train_dict:
            history_item = train_dict[user]
        else:
            history_item = []
        dist[i][history_item] = 1e6 
        ground_truth.append(user_gt)

    values, predicted_indices = dist.topk(50, largest=False, sorted=True, dim=-1)
    topk_list = [10, 20]
    results = computeTopNAccuracy(ground_truth, predicted_indices, topk_list)
    print(f'client {cnt}')
    print_results(results)
    client_result.append(results)

# get the overall results according to the test_num
overall_result = []
for metric, value in enumerate(results):
    for k in range(len(results[metric])):
        overall_result.append(np.average([client_result[i][metric][k] for i in range(len(client_result))], weights=test_num))
print('Overall results:')
print(overall_result)            
        


