#%%
import os
import json
import gzip
import numpy as np
import pandas as pd 
import matplotlib.pyplot as plt
import random
from collections import defaultdict
from tqdm import tqdm
random.seed(2024)
#%%
# get the item id to title map
save_path = './'
asin_to_feature_map = {}
with gzip.open('./raw_data/meta_Video_Games.json.gz', 'r') as file:
    for line in file:
        data = json.loads(line)
        if "asin" in data:
            asin_to_feature_map[data['asin']] = {}
            asin_to_feature_map[data['asin']]['title'] = data['title']
            asin_to_feature_map[data['asin']]['brand'] = data['brand']
            category = ' '.join(data['category'])
            asin_to_feature_map[data['asin']]['category'] = category

meta_file = './meta_data.json'
meta_f = open(meta_file, 'w', encoding='utf8')
json.dump(asin_to_feature_map, meta_f)
meta_f.close() 
