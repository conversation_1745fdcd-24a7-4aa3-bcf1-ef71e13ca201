# FELLRec联邦学习架构与代码实现对照

## 📊 架构概览

基于联邦学习架构图，本文档详细说明FELLRec项目中各个组件的代码实现位置和功能。

### 🏗️ 核心架构流程
```
用户设备 → 客户端 → 客户端模型 → 服务器 → 全局模型 → 推荐结果
   ↑                                                        ↓
   └─────────────────── 返回推荐结果 ←─────────────────────────┘
```

## 📋 架构组件与代码映射

| 架构组件 | 主要文件 | 关键函数 | 代码位置 | 核心功能 |
|---------|---------|---------|---------|---------|
| **用户设备** | `data/games/*.json` | 数据文件 | 数据目录 | 存储用户历史行为和游戏元数据 |
| **客户端** | `finetune.py` | `train()` | 187-1052行 | 客户端数据管理和训练协调 |
| **客户端模型** | `finetune.py` + `utils.py` | LoRA配置 | 450-521行 | LoRA微调和模型分割 |
| **服务器** | `utils.py` | `aggregate()` | utils.py | 模型聚合和相似度计算 |
| **全局模型** | `utils.py` | `get_aggregate_lora_weight()` | utils.py | 权重聚合和参数更新 |
| **推荐结果** | `inference.py` | `main()` + `evaluate()` | 70-421行 | 模型推理和结果生成 |

## 🔄 数据流向实现详解

### 1. 数据传输：用户设备 → 客户端

**功能**：将用户数据智能分配到不同客户端

**代码实现**：
```python
# 文件：finetune.py (591-617行)
# 基于用户嵌入的智能数据分割
if not os.path.exists('./data/train_client_data.pkl'):
    client_data, val_data, test_data = split_dataset(
        train_data, client_num, val_data, test_data, pretrain_emb_path
    )
    # 保存分割结果到本地
    with open('./data/train_client_data.pkl', 'wb') as file:
        pickle.dump(client_data, file)
```

**关键特点**：
- 使用K-means聚类基于用户嵌入进行分割
- 相似用户分配到同一客户端
- 数据本地缓存避免重复计算

### 2. 本地训练：客户端 → 客户端模型

**功能**：每个客户端使用本地数据训练个性化模型

**代码实现**：
```python
# 文件：finetune.py (710-845行)
for i in range(client_num):
    # 初始化客户端模型
    client[i] = LlamaForCausalLM.from_pretrained(base_model, load_in_8bit=True)
    client[i] = get_peft_model(client[i], config)
    
    # 配置训练器
    client_trainer[i] = Trainer(
        model=client[i],
        train_dataset=client_data[i],  # 客户端本地数据
        args=TrainingArguments(...)
    )
    
    # 执行本地训练
    client_trainer[i].train()
    client[i].save_pretrained(f'{output_dir}/client{i}_{save_name}')
```

**关键特点**：
- 每个客户端独立训练LoRA权重
- 使用8位量化优化内存使用
- 只训练1-2%的模型参数

### 3. 参数上传：客户端模型 → 服务器

**功能**：收集所有客户端的模型参数进行聚合

**代码实现**：
```python
# 文件：utils.py - aggregate()函数
def aggregate(output_dir, device_map, client_num, save_name, base_model):
    # 加载所有客户端的LoRA权重
    client_models = []
    for i in range(client_num):
        model = LlamaForCausalLM.from_pretrained(base_model)
        model = PeftModel.from_pretrained(
            model, f'{output_dir}/client{i}_{save_name}'
        )
        client_models.append(model)
    
    # 计算客户端间相似度矩阵
    similarity_matrix = compute_similarity_matrix(client_models)
    return similarity_matrix, accumulated_params
```

**关键特点**：
- 只传输LoRA权重，不传输完整模型
- 计算客户端模型间的余弦相似度
- 为智能聚合准备参数

### 4. 参数聚合：服务器 → 全局模型

**功能**：基于相似度智能聚合多个客户端的参数

**代码实现**：
```python
# 文件：finetune.py (847-905行)
# 计算相似度矩阵和聚合参数
sim_matrix, accumulated_params = aggregate(...)

# 将训练损失转换为权重分布
train_loss = softmax_with_temperature(train_loss)

for i in range(client_num):
    # 计算动态聚合权重
    warm_weight[i] = math.tanh(alpha/(train_loss[i]**(epoch+1/beta)))
    
    # 获取聚合后的LoRA权重
    lora_weight = get_aggregate_lora_weight(
        i, sim_matrix, accumulated_params, warm_weight[i], beta
    )
    
    # 更新客户端模型
    client[i].load_state_dict(lora_weight, strict=False)
```

**关键特点**：
- 基于相似度矩阵进行智能聚合
- 使用动态权重调整聚合强度
- 结合训练损失和epoch进度

### 5. 参数下载：全局模型 → 客户端模型

**功能**：客户端下载聚合后的全局模型参数

**代码实现**：
```python
# 文件：finetune.py (738行)
if epoch != 0:
    # 重新加载基础模型
    client[i] = LlamaForCausalLM.from_pretrained(base_model)
    client[i] = get_peft_model(client[i], config)
    
    # 加载聚合后的LoRA权重
    state_dict = torch.load(f'{output_dir}/client{i}_{save_name}/adapter_model.bin')
    client[i] = set_peft_model_state_dict(client[i], state_dict)
```

**关键特点**：
- 客户端重新初始化模型结构
- 加载服务器聚合后的权重
- 为下一轮训练做准备

### 6. 模型推理：客户端模型 → 推荐结果

**功能**：使用训练好的模型生成个性化推荐

**代码实现**：
```python
# 文件：inference.py (215-248行)
def evaluate(instructions, inputs, **kwargs):
    # 构建输入提示
    prompt = [generate_prompt(instruction, input) 
             for instruction, input in zip(instructions, inputs)]
    
    # 分词处理
    inputs = tokenizer(prompt, return_tensors="pt", padding=True)
    
    # 模型生成
    with torch.no_grad():
        generation_output = model.generate(
            **inputs,
            generation_config=generation_config,
            max_new_tokens=max_new_tokens,
        )
    
    # 解码结果
    output = tokenizer.batch_decode(generation_output.sequences)
    return [_.split('Response:\n')[-1] for _ in output]
```

**关键特点**：
- 将推荐任务转换为文本生成任务
- 使用批量推理提高效率
- 生成自然语言形式的推荐结果

### 7. 结果返回：推荐结果 → 用户设备

**功能**：将推荐结果返回给用户

**代码实现**：
```python
# 文件：inference.py (297-329行)
# 为每个测试样本添加预测结果
for i, test in enumerate(test_data):
    test_data[i]['predict'] = outputs[i]

# 保存推理结果
result_json_data = f'games_client{client_idx}.json'
with open(result_json_data, 'w', encoding='utf-8') as f:
    json.dump(result, f, indent=4, ensure_ascii=False)
```

**关键特点**：
- 结果以JSON格式保存
- 包含原始输入和模型预测
- 支持多客户端结果合并

## 🔗 核心算法实现

### 智能数据分割算法
```python
# 文件：utils.py - split_dataset()
def split_dataset(train_data, n, val_data, test_data, pretrain_emb_path):
    # 1. 加载预训练用户嵌入
    checkpoint = torch.load(pretrain_emb_path)
    user_embeddings = checkpoint['state_dict']['user_embedding.weight']
    
    # 2. K-means聚类分组用户
    from sklearn.cluster import KMeans
    kmeans = KMeans(n_clusters=n, random_state=42)
    user_clusters = kmeans.fit_predict(user_embeddings.numpy())
    
    # 3. 根据聚类结果分配数据
    client_data = [[] for _ in range(n)]
    for item in train_data:
        user_id = item['user']
        cluster_id = user_clusters[user_id]
        client_data[cluster_id].append(item)
    
    return client_data, val_data_split, test_data_split
```

### 相似度驱动聚合算法
```python
# 文件：utils.py - get_aggregate_lora_weight()
def get_aggregate_lora_weight(client_index, sim_matrix, accumulated_params, weight, beta):
    # 1. 获取当前客户端与其他客户端的相似度
    similarities = sim_matrix[client_index]
    
    # 2. 计算聚合权重
    aggregation_weights = torch.softmax(similarities * beta, dim=0)
    
    # 3. 加权聚合LoRA参数
    aggregated_params = {}
    for key in accumulated_params[0].keys():
        weighted_sum = torch.zeros_like(accumulated_params[0][key])
        for i, params in enumerate(accumulated_params):
            weighted_sum += aggregation_weights[i] * weight * params[key]
        aggregated_params[key] = weighted_sum
    
    return aggregated_params
```

### LoRA微调配置
```python
# 文件：finetune.py (466-475行)
config = LoraConfig(
    r=lora_r,                    # LoRA秩，控制新增参数量
    lora_alpha=lora_alpha,       # LoRA缩放参数
    target_modules=lora_target_modules,  # 目标模块（通常是注意力层）
    lora_dropout=lora_dropout,   # Dropout率，防止过拟合
    bias="none",                 # 不训练偏置参数
    task_type="CAUSAL_LM",      # 任务类型：因果语言模型
)
client[0] = get_peft_model(client[0], config)
```

## 📊 文件调用关系

### 主要执行流程
```
1. train.sh → finetune.py (启动训练)
2. finetune.py → utils.py (数据分割、模型聚合)
3. finetune.py → inference.py (推理生成)
4. inference.py → data/games/evaluate.py (性能评估)
```

### 关键函数调用链
```
训练阶段：
finetune.py:train()
├── utils.py:split_dataset()           # 数据分割
├── finetune.py:client_training_loop() # 客户端训练
├── utils.py:aggregate()               # 参数聚合
└── utils.py:get_aggregate_lora_weight() # 权重更新

推理阶段：
inference.py:main()
├── inference.py:evaluate()            # 批量推理
└── data/games/evaluate.py             # 性能评估
```

## 🎯 技术特性总结

### 隐私保护
- 数据本地化：用户数据不离开客户端
- 参数聚合：只共享模型参数，保护原始数据
- 智能分割：基于用户嵌入的相似性分组

### 计算效率
- LoRA微调：只训练1-2%的参数
- 8位量化：减少50%的内存使用
- 批量推理：提高推理效率

### 通信效率
- 稀疏更新：只传输LoRA权重
- 智能聚合：基于相似度减少无效聚合
- 动态权重：根据训练质量调整聚合强度

这个对照文档清晰地展示了FELLRec项目如何将联邦学习理论架构转化为具体的代码实现，为理解和使用该系统提供了完整的技术指南。
