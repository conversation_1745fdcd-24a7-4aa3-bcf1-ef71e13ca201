"""
联邦学习工具函数

提供联邦学习相关的辅助功能：
1. 日志设置
2. 模型保存和加载
3. 配置管理
4. 实验工具
"""

import os
import sys
import json
import yaml
import pickle
import logging
import torch
import numpy as np
import random
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime


def setup_logging(log_config: Dict[str, Any]):
    """
    设置日志系统
    
    Args:
        log_config: 日志配置
    """
    log_dir = log_config.get('log_dir', './logs')
    log_level = log_config.get('log_level', 'INFO')
    
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 设置根日志器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(
                os.path.join(log_dir, f'fedllm_srec_{datetime.now().strftime("%Y%m%d")}.log')
            )
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('transformers').setLevel(logging.WARNING)
    logging.getLogger('torch').setLevel(logging.WARNING)
    logging.getLogger('sklearn').setLevel(logging.WARNING)


def set_random_seed(seed: int):
    """
    设置随机种子
    
    Args:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def save_checkpoint(checkpoint: Dict[str, Any], filepath: str):
    """
    保存检查点
    
    Args:
        checkpoint: 检查点数据
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    torch.save(checkpoint, filepath)
    logging.info(f"Checkpoint saved to {filepath}")


def load_checkpoint(filepath: str) -> Dict[str, Any]:
    """
    加载检查点
    
    Args:
        filepath: 检查点路径
        
    Returns:
        checkpoint: 检查点数据
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Checkpoint not found: {filepath}")
    
    checkpoint = torch.load(filepath, map_location='cpu')
    logging.info(f"Checkpoint loaded from {filepath}")
    return checkpoint


def save_config(config: Dict[str, Any], filepath: str):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    with open(filepath, 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    logging.info(f"Config saved to {filepath}")


def load_config(filepath: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        filepath: 配置文件路径
        
    Returns:
        config: 配置字典
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Config file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        config = yaml.safe_load(f)
    
    logging.info(f"Config loaded from {filepath}")
    return config


def save_json(data: Any, filepath: str):
    """
    保存JSON文件
    
    Args:
        data: 要保存的数据
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    with open(filepath, 'w') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    logging.info(f"JSON data saved to {filepath}")


def load_json(filepath: str) -> Any:
    """
    加载JSON文件
    
    Args:
        filepath: JSON文件路径
        
    Returns:
        data: 加载的数据
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"JSON file not found: {filepath}")
    
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    logging.info(f"JSON data loaded from {filepath}")
    return data


def save_pickle(data: Any, filepath: str):
    """
    保存Pickle文件
    
    Args:
        data: 要保存的数据
        filepath: 保存路径
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    with open(filepath, 'wb') as f:
        pickle.dump(data, f)
    
    logging.info(f"Pickle data saved to {filepath}")


def load_pickle(filepath: str) -> Any:
    """
    加载Pickle文件
    
    Args:
        filepath: Pickle文件路径
        
    Returns:
        data: 加载的数据
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"Pickle file not found: {filepath}")
    
    with open(filepath, 'rb') as f:
        data = pickle.load(f)
    
    logging.info(f"Pickle data loaded from {filepath}")
    return data


def get_device(device_str: str = 'auto') -> torch.device:
    """
    获取计算设备
    
    Args:
        device_str: 设备字符串
        
    Returns:
        device: PyTorch设备
    """
    if device_str == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_str)
    
    logging.info(f"Using device: {device}")
    return device


def count_parameters(model: torch.nn.Module) -> Tuple[int, int]:
    """
    统计模型参数数量
    
    Args:
        model: PyTorch模型
        
    Returns:
        total_params: 总参数数量
        trainable_params: 可训练参数数量
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logging.info(f"Model parameters - Total: {total_params:,}, Trainable: {trainable_params:,}")
    
    return total_params, trainable_params


def format_time(seconds: float) -> str:
    """
    格式化时间
    
    Args:
        seconds: 秒数
        
    Returns:
        formatted_time: 格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{int(minutes)}m {seconds:.1f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{int(hours)}h {int(minutes)}m {seconds:.1f}s"


def format_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        formatted_size: 格式化的大小字符串
    """
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024**2:
        return f"{size_bytes/1024:.1f}KB"
    elif size_bytes < 1024**3:
        return f"{size_bytes/(1024**2):.1f}MB"
    else:
        return f"{size_bytes/(1024**3):.1f}GB"


def create_experiment_dir(base_dir: str, experiment_name: str) -> str:
    """
    创建实验目录
    
    Args:
        base_dir: 基础目录
        experiment_name: 实验名称
        
    Returns:
        experiment_dir: 实验目录路径
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_dir = os.path.join(base_dir, f"{experiment_name}_{timestamp}")
    
    os.makedirs(experiment_dir, exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'models'), exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'logs'), exist_ok=True)
    os.makedirs(os.path.join(experiment_dir, 'results'), exist_ok=True)
    
    logging.info(f"Experiment directory created: {experiment_dir}")
    return experiment_dir


def log_system_info():
    """记录系统信息"""
    import platform
    import psutil
    
    logging.info("=" * 50)
    logging.info("System Information")
    logging.info("=" * 50)
    logging.info(f"Platform: {platform.platform()}")
    logging.info(f"Python: {platform.python_version()}")
    logging.info(f"PyTorch: {torch.__version__}")
    
    # CPU信息
    logging.info(f"CPU: {platform.processor()}")
    logging.info(f"CPU Cores: {psutil.cpu_count()}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    logging.info(f"Memory: {format_size(memory.total)} (Available: {format_size(memory.available)})")
    
    # GPU信息
    if torch.cuda.is_available():
        logging.info(f"CUDA: {torch.version.cuda}")
        logging.info(f"GPU Count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory
            logging.info(f"GPU {i}: {gpu_name} ({format_size(gpu_memory)})")
    else:
        logging.info("CUDA: Not available")
    
    logging.info("=" * 50)


def validate_config(config: Dict[str, Any]) -> bool:
    """
    验证配置文件
    
    Args:
        config: 配置字典
        
    Returns:
        is_valid: 配置是否有效
    """
    required_keys = [
        'data', 'federated', 'client_model', 'server_model',
        'training', 'evaluation', 'logging'
    ]
    
    for key in required_keys:
        if key not in config:
            logging.error(f"Missing required config key: {key}")
            return False
    
    # 验证数据配置
    data_config = config['data']
    if 'dataset' not in data_config or 'data_dir' not in data_config:
        logging.error("Missing required data config keys")
        return False
    
    # 验证联邦学习配置
    fed_config = config['federated']
    if 'client_num' not in fed_config or 'fed_rounds' not in fed_config:
        logging.error("Missing required federated config keys")
        return False
    
    logging.info("Config validation passed")
    return True


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并配置文件
    
    Args:
        base_config: 基础配置
        override_config: 覆盖配置
        
    Returns:
        merged_config: 合并后的配置
    """
    merged_config = base_config.copy()
    
    for key, value in override_config.items():
        if key in merged_config and isinstance(merged_config[key], dict) and isinstance(value, dict):
            merged_config[key] = merge_configs(merged_config[key], value)
        else:
            merged_config[key] = value
    
    return merged_config


class ExperimentTracker:
    """实验跟踪器"""
    
    def __init__(self, experiment_dir: str):
        self.experiment_dir = experiment_dir
        self.metrics_file = os.path.join(experiment_dir, 'metrics.json')
        self.metrics = []
    
    def log_metrics(self, round_num: int, metrics: Dict[str, Any]):
        """记录指标"""
        entry = {
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            'metrics': metrics
        }
        self.metrics.append(entry)
        
        # 保存到文件
        save_json(self.metrics, self.metrics_file)
    
    def get_best_metrics(self, metric_name: str = 'ndcg@10') -> Tuple[int, Dict[str, Any]]:
        """获取最佳指标"""
        if not self.metrics:
            return -1, {}
        
        best_round = -1
        best_value = -1
        best_metrics = {}
        
        for entry in self.metrics:
            if 'global_metrics' in entry['metrics']:
                value = entry['metrics']['global_metrics'].get(metric_name, 0)
                if value > best_value:
                    best_value = value
                    best_round = entry['round']
                    best_metrics = entry['metrics']
        
        return best_round, best_metrics
    
    def plot_metrics(self, save_path: str = None):
        """绘制指标曲线"""
        try:
            import matplotlib.pyplot as plt
            
            if not self.metrics:
                return
            
            rounds = [entry['round'] for entry in self.metrics]
            ndcg_values = []
            hr_values = []
            
            for entry in self.metrics:
                global_metrics = entry['metrics'].get('global_metrics', {})
                ndcg_values.append(global_metrics.get('ndcg@10', 0))
                hr_values.append(global_metrics.get('hit_rate@10', 0))
            
            plt.figure(figsize=(12, 5))
            
            plt.subplot(1, 2, 1)
            plt.plot(rounds, ndcg_values, 'b-', marker='o')
            plt.title('NDCG@10')
            plt.xlabel('Round')
            plt.ylabel('NDCG@10')
            plt.grid(True)
            
            plt.subplot(1, 2, 2)
            plt.plot(rounds, hr_values, 'r-', marker='s')
            plt.title('Hit Rate@10')
            plt.xlabel('Round')
            plt.ylabel('Hit Rate@10')
            plt.grid(True)
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logging.info(f"Metrics plot saved to {save_path}")
            else:
                plt.show()
            
            plt.close()
            
        except ImportError:
            logging.warning("Matplotlib not available, skipping plot generation")


def main():
    """测试工具函数"""
    # 测试日志设置
    log_config = {
        'log_dir': './test_logs',
        'log_level': 'INFO'
    }
    setup_logging(log_config)
    
    # 记录系统信息
    log_system_info()
    
    # 测试配置保存和加载
    test_config = {
        'experiment': 'test',
        'parameters': {
            'learning_rate': 0.001,
            'batch_size': 32
        }
    }
    
    save_config(test_config, './test_logs/test_config.yaml')
    loaded_config = load_config('./test_logs/test_config.yaml')
    
    logging.info(f"Config test passed: {loaded_config == test_config}")
    
    # 测试实验跟踪器
    tracker = ExperimentTracker('./test_logs')
    tracker.log_metrics(1, {'global_metrics': {'ndcg@10': 0.1, 'hit_rate@10': 0.05}})
    tracker.log_metrics(2, {'global_metrics': {'ndcg@10': 0.15, 'hit_rate@10': 0.08}})
    
    best_round, best_metrics = tracker.get_best_metrics()
    logging.info(f"Best round: {best_round}, Best NDCG@10: {best_metrics['global_metrics']['ndcg@10']}")


if __name__ == '__main__':
    main()
