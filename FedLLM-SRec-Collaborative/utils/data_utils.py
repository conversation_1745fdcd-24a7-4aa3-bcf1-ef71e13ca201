"""
数据处理工具 - 联邦数据分割和加载

基于FELLRec和LLM-SRec的数据处理逻辑，实现：
1. 联邦数据分割（基于用户嵌入的聚类）
2. 数据预处理和序列化
3. 联邦数据加载器
"""

import os
import json
import pickle
import gzip
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.cluster import KMeans
from sklearn.preprocessing import LabelEncoder
from typing import Dict, List, Tuple, Optional, Any
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class SequenceDataset(Dataset):
    """序列推荐数据集"""
    
    def __init__(self, sequences: List[Dict], max_length: int = 128, item_encoder: Optional[LabelEncoder] = None):
        self.sequences = sequences
        self.max_length = max_length
        self.item_encoder = item_encoder
        
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        
        # 获取用户序列
        user_id = sequence['user']
        item_sequence = sequence['sequence']
        target_item = sequence['target']
        
        # 截断或填充序列
        if len(item_sequence) > self.max_length:
            item_sequence = item_sequence[-self.max_length:]
        
        # 转换为张量
        padded_sequence = [0] * (self.max_length - len(item_sequence)) + item_sequence
        
        return {
            'user_id': torch.tensor(user_id, dtype=torch.long),
            'user_sequences': torch.tensor(padded_sequence, dtype=torch.long),
            'target_items': torch.tensor(target_item, dtype=torch.long),
            'sequence_length': torch.tensor(len(item_sequence), dtype=torch.long)
        }


class FederatedDataLoader:
    """联邦数据加载器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.data_config = config['data']
        self.fed_config = config['federated']
        
        self.dataset_name = self.data_config['dataset']
        self.data_dir = self.data_config['data_dir']
        self.max_length = self.data_config['max_sequence_length']
        self.client_num = self.fed_config['client_num']
        
        # 数据分割参数
        self.split_method = self.fed_config.get('data_split_method', 'user_embedding')
        self.split_alpha = self.fed_config.get('split_alpha', 0.5)
        
        logger.info(f"FederatedDataLoader initialized for dataset: {self.dataset_name}")
    
    def load_raw_data(self) -> Tuple[List[Dict], Dict[str, Any]]:
        """
        加载原始数据
        
        Returns:
            interactions: 用户交互数据
            metadata: 数据元信息
        """
        # 数据文件路径
        data_path = os.path.join(self.data_dir, 'raw', self.dataset_name)
        
        # 加载交互数据
        interactions_file = os.path.join(data_path, 'interactions.json')
        if os.path.exists(interactions_file):
            with open(interactions_file, 'r') as f:
                interactions = json.load(f)
        else:
            # 如果没有预处理的数据，从原始文件加载
            interactions = self._load_from_raw_files(data_path)
        
        # 加载元数据
        metadata_file = os.path.join(data_path, 'metadata.json')
        if os.path.exists(metadata_file):
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
        else:
            metadata = self._generate_metadata(interactions)
        
        logger.info(f"Loaded {len(interactions)} interactions from {self.dataset_name}")
        
        return interactions, metadata
    
    def _load_from_raw_files(self, data_path: str) -> List[Dict]:
        """从原始文件加载数据"""
        interactions = []
        
        # 尝试加载不同格式的文件
        for filename in os.listdir(data_path):
            if filename.endswith('.json'):
                file_path = os.path.join(data_path, filename)
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        interactions.extend(data)
                    elif isinstance(data, dict):
                        interactions.append(data)
        
        return interactions
    
    def _generate_metadata(self, interactions: List[Dict]) -> Dict[str, Any]:
        """生成数据元信息"""
        users = set()
        items = set()
        
        for interaction in interactions:
            users.add(interaction.get('user', interaction.get('user_id')))
            if 'sequence' in interaction:
                items.update(interaction['sequence'])
            if 'target' in interaction:
                items.add(interaction['target'])
            if 'item' in interaction:
                items.add(interaction['item'])
        
        metadata = {
            'num_users': len(users),
            'num_items': len(items),
            'num_interactions': len(interactions),
            'user_list': sorted(list(users)),
            'item_list': sorted(list(items))
        }
        
        return metadata
    
    def preprocess_data(self, interactions: List[Dict], metadata: Dict[str, Any]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """
        数据预处理
        
        Args:
            interactions: 原始交互数据
            metadata: 数据元信息
            
        Returns:
            train_data: 训练数据
            val_data: 验证数据
            test_data: 测试数据
        """
        # 按用户分组
        user_interactions = defaultdict(list)
        for interaction in interactions:
            user_id = interaction.get('user', interaction.get('user_id'))
            user_interactions[user_id].append(interaction)
        
        # 为每个用户创建序列
        sequences = []
        for user_id, user_data in user_interactions.items():
            # 按时间排序（如果有时间戳）
            if 'timestamp' in user_data[0]:
                user_data.sort(key=lambda x: x['timestamp'])
            
            # 提取物品序列
            items = []
            for interaction in user_data:
                if 'item' in interaction:
                    items.append(interaction['item'])
                elif 'item_id' in interaction:
                    items.append(interaction['item_id'])
            
            # 过滤短序列
            if len(items) < self.data_config.get('min_interactions', 5):
                continue
            
            # 创建训练序列
            for i in range(1, len(items)):
                sequence = {
                    'user': user_id,
                    'sequence': items[:i],
                    'target': items[i]
                }
                sequences.append(sequence)
        
        # 数据分割
        train_ratio = self.data_config.get('train_ratio', 0.8)
        val_ratio = self.data_config.get('val_ratio', 0.1)
        
        # 随机打乱
        np.random.shuffle(sequences)
        
        # 分割数据
        n_train = int(len(sequences) * train_ratio)
        n_val = int(len(sequences) * val_ratio)
        
        train_data = sequences[:n_train]
        val_data = sequences[n_train:n_train + n_val]
        test_data = sequences[n_train + n_val:]
        
        logger.info(f"Data split: Train={len(train_data)}, Val={len(val_data)}, Test={len(test_data)}")
        
        return train_data, val_data, test_data
    
    def split_federated_data(self, train_data: List[Dict], val_data: List[Dict], 
                           test_data: List[Dict]) -> Tuple[List[List[Dict]], List[List[Dict]], List[Dict]]:
        """
        联邦数据分割
        
        Args:
            train_data: 训练数据
            val_data: 验证数据
            test_data: 测试数据
            
        Returns:
            client_train_data: 客户端训练数据列表
            client_val_data: 客户端验证数据列表
            global_test_data: 全局测试数据
        """
        if self.split_method == 'user_embedding':
            return self._split_by_user_embedding(train_data, val_data, test_data)
        elif self.split_method == 'random':
            return self._split_randomly(train_data, val_data, test_data)
        elif self.split_method == 'iid':
            return self._split_iid(train_data, val_data, test_data)
        else:
            raise ValueError(f"Unknown split method: {self.split_method}")
    
    def _split_by_user_embedding(self, train_data: List[Dict], val_data: List[Dict], 
                               test_data: List[Dict]) -> Tuple[List[List[Dict]], List[List[Dict]], List[Dict]]:
        """基于用户嵌入的聚类分割"""
        # 获取所有用户
        all_users = set()
        for data in [train_data, val_data, test_data]:
            for item in data:
                all_users.add(item['user'])
        all_users = list(all_users)
        
        # 生成用户嵌入（这里使用随机嵌入作为示例）
        # 在实际应用中，应该使用预训练的用户嵌入
        embedding_dim = 64
        user_embeddings = np.random.normal(0, 1, (len(all_users), embedding_dim))
        
        # K-means聚类
        kmeans = KMeans(n_clusters=self.client_num, random_state=42)
        cluster_labels = kmeans.fit_predict(user_embeddings)
        
        # 创建用户到客户端的映射
        user_to_client = {}
        for user, label in zip(all_users, cluster_labels):
            user_to_client[user] = label
        
        # 分割数据
        client_train_data = [[] for _ in range(self.client_num)]
        client_val_data = [[] for _ in range(self.client_num)]
        
        for item in train_data:
            client_id = user_to_client[item['user']]
            client_train_data[client_id].append(item)
        
        for item in val_data:
            client_id = user_to_client[item['user']]
            client_val_data[client_id].append(item)
        
        logger.info(f"Split data by user embedding clustering into {self.client_num} clients")
        for i in range(self.client_num):
            logger.info(f"Client {i}: Train={len(client_train_data[i])}, Val={len(client_val_data[i])}")
        
        return client_train_data, client_val_data, test_data
    
    def _split_randomly(self, train_data: List[Dict], val_data: List[Dict], 
                       test_data: List[Dict]) -> Tuple[List[List[Dict]], List[List[Dict]], List[Dict]]:
        """随机分割"""
        client_train_data = [[] for _ in range(self.client_num)]
        client_val_data = [[] for _ in range(self.client_num)]
        
        # 随机分配训练数据
        for i, item in enumerate(train_data):
            client_id = i % self.client_num
            client_train_data[client_id].append(item)
        
        # 随机分配验证数据
        for i, item in enumerate(val_data):
            client_id = i % self.client_num
            client_val_data[client_id].append(item)
        
        return client_train_data, client_val_data, test_data
    
    def _split_iid(self, train_data: List[Dict], val_data: List[Dict], 
                  test_data: List[Dict]) -> Tuple[List[List[Dict]], List[List[Dict]], List[Dict]]:
        """IID分割（独立同分布）"""
        # 随机打乱数据
        np.random.shuffle(train_data)
        np.random.shuffle(val_data)
        
        # 均匀分割
        train_size_per_client = len(train_data) // self.client_num
        val_size_per_client = len(val_data) // self.client_num
        
        client_train_data = []
        client_val_data = []
        
        for i in range(self.client_num):
            start_train = i * train_size_per_client
            end_train = (i + 1) * train_size_per_client if i < self.client_num - 1 else len(train_data)
            
            start_val = i * val_size_per_client
            end_val = (i + 1) * val_size_per_client if i < self.client_num - 1 else len(val_data)
            
            client_train_data.append(train_data[start_train:end_train])
            client_val_data.append(val_data[start_val:end_val])
        
        return client_train_data, client_val_data, test_data
    
    def create_dataloaders(self, client_train_data: List[List[Dict]], 
                          client_val_data: List[List[Dict]], 
                          test_data: List[Dict]) -> Tuple[List[DataLoader], List[DataLoader], DataLoader]:
        """
        创建数据加载器
        
        Returns:
            client_train_loaders: 客户端训练数据加载器列表
            client_val_loaders: 客户端验证数据加载器列表
            test_loader: 测试数据加载器
        """
        # 创建物品编码器
        all_items = set()
        for data_list in [client_train_data, client_val_data, [test_data]]:
            for data in data_list:
                if isinstance(data, list):
                    for item in data:
                        if 'sequence' in item:
                            all_items.update(item['sequence'])
                        if 'target' in item:
                            all_items.add(item['target'])
                else:
                    if 'sequence' in data:
                        all_items.update(data['sequence'])
                    if 'target' in data:
                        all_items.add(data['target'])
        
        item_encoder = LabelEncoder()
        item_encoder.fit(list(all_items))
        
        # 编码数据
        def encode_data(data_list):
            encoded_data = []
            for item in data_list:
                encoded_item = item.copy()
                encoded_item['sequence'] = item_encoder.transform(item['sequence'])
                encoded_item['target'] = item_encoder.transform([item['target']])[0]
                encoded_data.append(encoded_item)
            return encoded_data
        
        # 创建客户端训练数据加载器
        client_train_loaders = []
        for client_data in client_train_data:
            if client_data:  # 检查是否为空
                encoded_data = encode_data(client_data)
                dataset = SequenceDataset(encoded_data, self.max_length, item_encoder)
                loader = DataLoader(
                    dataset,
                    batch_size=self.config['client_model']['batch_size'],
                    shuffle=True,
                    num_workers=self.config['hardware'].get('num_workers', 4),
                    pin_memory=self.config['hardware'].get('pin_memory', True)
                )
                client_train_loaders.append(loader)
            else:
                # 创建空的数据加载器
                empty_dataset = SequenceDataset([], self.max_length, item_encoder)
                empty_loader = DataLoader(empty_dataset, batch_size=1)
                client_train_loaders.append(empty_loader)
        
        # 创建客户端验证数据加载器
        client_val_loaders = []
        for client_data in client_val_data:
            if client_data:
                encoded_data = encode_data(client_data)
                dataset = SequenceDataset(encoded_data, self.max_length, item_encoder)
                loader = DataLoader(
                    dataset,
                    batch_size=self.config['evaluation']['eval_batch_size'],
                    shuffle=False,
                    num_workers=self.config['hardware'].get('num_workers', 4),
                    pin_memory=self.config['hardware'].get('pin_memory', True)
                )
                client_val_loaders.append(loader)
            else:
                empty_dataset = SequenceDataset([], self.max_length, item_encoder)
                empty_loader = DataLoader(empty_dataset, batch_size=1)
                client_val_loaders.append(empty_loader)
        
        # 创建测试数据加载器
        encoded_test_data = encode_data(test_data)
        test_dataset = SequenceDataset(encoded_test_data, self.max_length, item_encoder)
        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config['evaluation']['eval_batch_size'],
            shuffle=False,
            num_workers=self.config['hardware'].get('num_workers', 4),
            pin_memory=self.config['hardware'].get('pin_memory', True)
        )
        
        logger.info(f"Created {len(client_train_loaders)} client train loaders and 1 test loader")
        
        return client_train_loaders, client_val_loaders, test_loader
    
    def get_federated_dataloaders(self) -> Tuple[List[DataLoader], List[DataLoader], DataLoader]:
        """
        获取联邦数据加载器（主接口）
        
        Returns:
            client_train_loaders: 客户端训练数据加载器列表
            client_val_loaders: 客户端验证数据加载器列表
            test_loader: 测试数据加载器
        """
        # 1. 加载原始数据
        interactions, metadata = self.load_raw_data()
        
        # 2. 数据预处理
        train_data, val_data, test_data = self.preprocess_data(interactions, metadata)
        
        # 3. 联邦数据分割
        client_train_data, client_val_data, global_test_data = self.split_federated_data(
            train_data, val_data, test_data
        )
        
        # 4. 创建数据加载器
        client_train_loaders, client_val_loaders, test_loader = self.create_dataloaders(
            client_train_data, client_val_data, global_test_data
        )
        
        return client_train_loaders, client_val_loaders, test_loader
    
    def save_federated_data(self, client_train_data: List[List[Dict]], 
                           client_val_data: List[List[Dict]], test_data: List[Dict]):
        """保存联邦分割后的数据"""
        federated_dir = self.data_config['federated_data_dir']
        os.makedirs(federated_dir, exist_ok=True)
        
        # 保存客户端数据
        for i, (train_data, val_data) in enumerate(zip(client_train_data, client_val_data)):
            client_dir = os.path.join(federated_dir, f'client_{i}')
            os.makedirs(client_dir, exist_ok=True)
            
            with open(os.path.join(client_dir, 'train.json'), 'w') as f:
                json.dump(train_data, f)
            
            with open(os.path.join(client_dir, 'val.json'), 'w') as f:
                json.dump(val_data, f)
        
        # 保存测试数据
        with open(os.path.join(federated_dir, 'test.json'), 'w') as f:
            json.dump(test_data, f)
        
        logger.info(f"Federated data saved to {federated_dir}")


def prepare_sample_data(data_dir: str, dataset_name: str = "Movies_and_TV"):
    """准备示例数据"""
    sample_data_dir = os.path.join(data_dir, 'raw', dataset_name)
    os.makedirs(sample_data_dir, exist_ok=True)
    
    # 生成示例交互数据
    np.random.seed(42)
    num_users = 1000
    num_items = 5000
    num_interactions = 50000
    
    interactions = []
    for _ in range(num_interactions):
        user_id = np.random.randint(1, num_users + 1)
        item_id = np.random.randint(1, num_items + 1)
        timestamp = np.random.randint(1000000000, 2000000000)
        
        interactions.append({
            'user': user_id,
            'item': item_id,
            'timestamp': timestamp,
            'rating': np.random.randint(1, 6)
        })
    
    # 保存数据
    with open(os.path.join(sample_data_dir, 'interactions.json'), 'w') as f:
        json.dump(interactions, f)
    
    logger.info(f"Sample data created at {sample_data_dir}")


if __name__ == '__main__':
    # 测试数据加载器
    config = {
        'data': {
            'dataset': 'Movies_and_TV',
            'data_dir': './data',
            'max_sequence_length': 128,
            'min_interactions': 5,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        },
        'federated': {
            'client_num': 5,
            'data_split_method': 'user_embedding'
        },
        'client_model': {
            'batch_size': 32
        },
        'evaluation': {
            'eval_batch_size': 64
        },
        'hardware': {
            'num_workers': 4,
            'pin_memory': True
        }
    }
    
    # 准备示例数据
    prepare_sample_data('./data')
    
    # 测试数据加载器
    data_loader = FederatedDataLoader(config)
    client_train_loaders, client_val_loaders, test_loader = data_loader.get_federated_dataloaders()
    
    print(f"Created {len(client_train_loaders)} client loaders")
    print(f"Test loader has {len(test_loader)} batches")
