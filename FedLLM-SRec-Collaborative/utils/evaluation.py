"""
评估工具 - 推荐系统性能评估

实现推荐系统的标准评估指标：
1. NDCG (Normalized Discounted Cumulative Gain)
2. Hit Rate (HR)
3. Recall
4. Precision
5. 联邦学习特定指标
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.metrics import roc_auc_score, average_precision_score
from torch.utils.data import DataLoader
import time

logger = logging.getLogger(__name__)


class RecommendationEvaluator:
    """推荐系统评估器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.eval_config = config['evaluation']
        self.metrics = self.eval_config.get('metrics', ['ndcg', 'hit_rate', 'recall', 'precision'])
        self.top_k_list = self.eval_config.get('top_k', [5, 10, 20])
        self.device = config.get('device', 'cuda:0')
        
        logger.info(f"RecommendationEvaluator initialized with metrics: {self.metrics}")
    
    def compute_ndcg(self, predictions: torch.Tensor, targets: torch.Tensor, k: int) -> float:
        """
        计算NDCG@K
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            k: Top-K
            
        Returns:
            ndcg: NDCG@K分数
        """
        batch_size = predictions.shape[0]
        
        # 获取Top-K预测
        _, top_k_indices = torch.topk(predictions, k, dim=-1)
        
        ndcg_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            if len(relevant_items) == 0:
                continue
            
            # 计算DCG@K
            dcg = 0.0
            for j, item_idx in enumerate(top_k_indices[i]):
                if item_idx in relevant_items:
                    dcg += 1.0 / np.log2(j + 2)  # j+2 because log2(1)=0
            
            # 计算IDCG@K
            idcg = 0.0
            for j in range(min(k, len(relevant_items))):
                idcg += 1.0 / np.log2(j + 2)
            
            # 计算NDCG
            if idcg > 0:
                ndcg_scores.append(dcg / idcg)
        
        return np.mean(ndcg_scores) if ndcg_scores else 0.0
    
    def compute_hit_rate(self, predictions: torch.Tensor, targets: torch.Tensor, k: int) -> float:
        """
        计算Hit Rate@K
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            k: Top-K
            
        Returns:
            hit_rate: Hit Rate@K分数
        """
        batch_size = predictions.shape[0]
        
        # 获取Top-K预测
        _, top_k_indices = torch.topk(predictions, k, dim=-1)
        
        hits = 0
        total = 0
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            if len(relevant_items) == 0:
                continue
            
            # 检查是否命中
            hit = any(item_idx in relevant_items for item_idx in top_k_indices[i])
            if hit:
                hits += 1
            total += 1
        
        return hits / total if total > 0 else 0.0
    
    def compute_recall(self, predictions: torch.Tensor, targets: torch.Tensor, k: int) -> float:
        """
        计算Recall@K
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            k: Top-K
            
        Returns:
            recall: Recall@K分数
        """
        batch_size = predictions.shape[0]
        
        # 获取Top-K预测
        _, top_k_indices = torch.topk(predictions, k, dim=-1)
        
        recall_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            if len(relevant_items) == 0:
                continue
            
            # 计算召回率
            hit_items = [item_idx for item_idx in top_k_indices[i] if item_idx in relevant_items]
            recall = len(hit_items) / len(relevant_items)
            recall_scores.append(recall)
        
        return np.mean(recall_scores) if recall_scores else 0.0
    
    def compute_precision(self, predictions: torch.Tensor, targets: torch.Tensor, k: int) -> float:
        """
        计算Precision@K
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            k: Top-K
            
        Returns:
            precision: Precision@K分数
        """
        batch_size = predictions.shape[0]
        
        # 获取Top-K预测
        _, top_k_indices = torch.topk(predictions, k, dim=-1)
        
        precision_scores = []
        
        for i in range(batch_size):
            # 获取真实相关物品
            relevant_items = torch.nonzero(targets[i]).squeeze(-1)
            if len(relevant_items) == 0:
                continue
            
            # 计算精确率
            hit_items = [item_idx for item_idx in top_k_indices[i] if item_idx in relevant_items]
            precision = len(hit_items) / k
            precision_scores.append(precision)
        
        return np.mean(precision_scores) if precision_scores else 0.0
    
    def compute_auc(self, predictions: torch.Tensor, targets: torch.Tensor) -> float:
        """
        计算AUC
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            
        Returns:
            auc: AUC分数
        """
        # 展平张量
        pred_flat = predictions.cpu().numpy().flatten()
        target_flat = targets.cpu().numpy().flatten()
        
        # 过滤有效样本
        valid_mask = ~np.isnan(pred_flat) & ~np.isnan(target_flat)
        pred_valid = pred_flat[valid_mask]
        target_valid = target_flat[valid_mask]
        
        if len(np.unique(target_valid)) < 2:
            return 0.0
        
        try:
            auc = roc_auc_score(target_valid, pred_valid)
            return auc
        except ValueError:
            return 0.0
    
    def evaluate_batch(self, predictions: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
        """
        评估单个批次
        
        Args:
            predictions: 预测分数 [batch_size, num_items]
            targets: 真实标签 [batch_size, num_items] (0/1)
            
        Returns:
            metrics: 评估指标字典
        """
        metrics = {}
        
        for k in self.top_k_list:
            if 'ndcg' in self.metrics:
                metrics[f'ndcg@{k}'] = self.compute_ndcg(predictions, targets, k)
            
            if 'hit_rate' in self.metrics:
                metrics[f'hit_rate@{k}'] = self.compute_hit_rate(predictions, targets, k)
            
            if 'recall' in self.metrics:
                metrics[f'recall@{k}'] = self.compute_recall(predictions, targets, k)
            
            if 'precision' in self.metrics:
                metrics[f'precision@{k}'] = self.compute_precision(predictions, targets, k)
        
        if 'auc' in self.metrics:
            metrics['auc'] = self.compute_auc(predictions, targets)
        
        return metrics
    
    def evaluate_model(self, model: nn.Module, data_loader: DataLoader) -> Dict[str, float]:
        """
        评估模型
        
        Args:
            model: 要评估的模型
            data_loader: 数据加载器
            
        Returns:
            metrics: 平均评估指标
        """
        model.eval()
        
        all_metrics = {f'{metric}@{k}': [] for metric in self.metrics for k in self.top_k_list}
        if 'auc' in self.metrics:
            all_metrics['auc'] = []
        
        total_samples = 0
        start_time = time.time()
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(data_loader):
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # 模型推理
                outputs = model(batch, mode='eval')
                
                # 准备评估数据
                if 'recommendation_scores' in outputs:
                    predictions = outputs['recommendation_scores']
                    targets = self._prepare_targets(batch, predictions.shape[-1])
                    
                    # 计算批次指标
                    batch_metrics = self.evaluate_batch(predictions, targets)
                    
                    # 累积指标
                    for metric_name, value in batch_metrics.items():
                        if metric_name in all_metrics:
                            all_metrics[metric_name].append(value)
                
                total_samples += batch['user_sequences'].shape[0]
                
                # 限制评估样本数量
                max_samples = self.eval_config.get('num_eval_samples', -1)
                if max_samples > 0 and total_samples >= max_samples:
                    break
        
        # 计算平均指标
        avg_metrics = {}
        for metric_name, values in all_metrics.items():
            if values:
                avg_metrics[metric_name] = np.mean(values)
            else:
                avg_metrics[metric_name] = 0.0
        
        eval_time = time.time() - start_time
        avg_metrics['eval_time'] = eval_time
        avg_metrics['num_samples'] = total_samples
        
        logger.info(f"Evaluation completed. Samples: {total_samples}, Time: {eval_time:.2f}s")
        
        return avg_metrics
    
    def _prepare_targets(self, batch: Dict[str, torch.Tensor], num_items: int) -> torch.Tensor:
        """
        准备目标标签
        
        Args:
            batch: 批次数据
            num_items: 物品总数
            
        Returns:
            targets: 目标标签 [batch_size, num_items]
        """
        batch_size = batch['user_sequences'].shape[0]
        targets = torch.zeros(batch_size, num_items, device=self.device)
        
        # 设置目标物品为1
        if 'target_items' in batch:
            target_items = batch['target_items']
            for i, target_item in enumerate(target_items):
                if target_item < num_items:
                    targets[i, target_item] = 1.0
        
        return targets
    
    def evaluate_federated_system(self, global_model: nn.Module, client_models: List[nn.Module],
                                 test_loader: DataLoader, client_val_loaders: List[DataLoader]) -> Dict[str, Any]:
        """
        评估联邦推荐系统
        
        Args:
            global_model: 全局模型
            client_models: 客户端模型列表
            test_loader: 测试数据加载器
            client_val_loaders: 客户端验证数据加载器列表
            
        Returns:
            fed_metrics: 联邦评估指标
        """
        fed_metrics = {}
        
        # 1. 评估全局模型
        logger.info("Evaluating global model")
        global_metrics = self.evaluate_model(global_model, test_loader)
        fed_metrics['global'] = global_metrics
        
        # 2. 评估客户端模型
        logger.info("Evaluating client models")
        client_metrics = []
        for i, (client_model, val_loader) in enumerate(zip(client_models, client_val_loaders)):
            if len(val_loader) > 0:  # 检查是否有数据
                client_metric = self.evaluate_model(client_model, val_loader)
                client_metrics.append(client_metric)
                logger.info(f"Client {i} - NDCG@10: {client_metric.get('ndcg@10', 0.0):.4f}")
            else:
                client_metrics.append({})
        
        fed_metrics['clients'] = client_metrics
        
        # 3. 计算联邦指标
        fed_metrics['federated'] = self._compute_federated_metrics(global_metrics, client_metrics)
        
        return fed_metrics
    
    def _compute_federated_metrics(self, global_metrics: Dict[str, float], 
                                 client_metrics: List[Dict[str, float]]) -> Dict[str, float]:
        """
        计算联邦学习特定指标
        
        Args:
            global_metrics: 全局模型指标
            client_metrics: 客户端模型指标列表
            
        Returns:
            fed_metrics: 联邦指标
        """
        fed_metrics = {}
        
        # 计算客户端平均性能
        valid_clients = [m for m in client_metrics if m]
        if valid_clients:
            for metric_name in valid_clients[0].keys():
                if metric_name not in ['eval_time', 'num_samples']:
                    values = [m[metric_name] for m in valid_clients if metric_name in m]
                    if values:
                        fed_metrics[f'client_avg_{metric_name}'] = np.mean(values)
                        fed_metrics[f'client_std_{metric_name}'] = np.std(values)
        
        # 计算性能提升
        if valid_clients and global_metrics:
            for metric_name in ['ndcg@10', 'hit_rate@10']:
                if metric_name in global_metrics:
                    global_value = global_metrics[metric_name]
                    client_values = [m.get(metric_name, 0.0) for m in valid_clients]
                    if client_values:
                        avg_client_value = np.mean(client_values)
                        improvement = (global_value - avg_client_value) / avg_client_value if avg_client_value > 0 else 0.0
                        fed_metrics[f'{metric_name}_improvement'] = improvement
        
        # 计算客户端多样性
        if len(valid_clients) > 1:
            ndcg_values = [m.get('ndcg@10', 0.0) for m in valid_clients]
            fed_metrics['client_diversity'] = np.std(ndcg_values) / np.mean(ndcg_values) if np.mean(ndcg_values) > 0 else 0.0
        
        return fed_metrics
    
    def compute_communication_cost(self, model_params: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        计算通信开销
        
        Args:
            model_params: 模型参数
            
        Returns:
            comm_cost: 通信开销指标
        """
        total_params = 0
        total_size_mb = 0.0
        
        for param in model_params.values():
            num_params = param.numel()
            size_mb = num_params * 4 / (1024 * 1024)  # 假设float32，4字节
            
            total_params += num_params
            total_size_mb += size_mb
        
        comm_cost = {
            'total_parameters': total_params,
            'total_size_mb': total_size_mb,
            'avg_param_size': total_size_mb / len(model_params) if model_params else 0.0
        }
        
        return comm_cost
    
    def generate_evaluation_report(self, metrics: Dict[str, Any]) -> str:
        """
        生成评估报告
        
        Args:
            metrics: 评估指标
            
        Returns:
            report: 评估报告字符串
        """
        report = []
        report.append("=" * 60)
        report.append("FedLLM-SRec Evaluation Report")
        report.append("=" * 60)
        
        # 全局模型性能
        if 'global' in metrics:
            report.append("\n📊 Global Model Performance:")
            global_metrics = metrics['global']
            for k in self.top_k_list:
                if f'ndcg@{k}' in global_metrics:
                    report.append(f"  NDCG@{k}: {global_metrics[f'ndcg@{k}']:.4f}")
                if f'hit_rate@{k}' in global_metrics:
                    report.append(f"  Hit Rate@{k}: {global_metrics[f'hit_rate@{k}']:.4f}")
        
        # 客户端性能
        if 'clients' in metrics:
            report.append("\n👥 Client Performance:")
            client_metrics = metrics['clients']
            for i, client_metric in enumerate(client_metrics):
                if client_metric:
                    ndcg = client_metric.get('ndcg@10', 0.0)
                    hr = client_metric.get('hit_rate@10', 0.0)
                    report.append(f"  Client {i}: NDCG@10={ndcg:.4f}, HR@10={hr:.4f}")
        
        # 联邦指标
        if 'federated' in metrics:
            report.append("\n🔗 Federated Metrics:")
            fed_metrics = metrics['federated']
            for metric_name, value in fed_metrics.items():
                report.append(f"  {metric_name}: {value:.4f}")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)


def main():
    """测试评估器"""
    config = {
        'evaluation': {
            'metrics': ['ndcg', 'hit_rate', 'recall', 'precision'],
            'top_k': [5, 10, 20],
            'eval_batch_size': 64,
            'num_eval_samples': 1000
        },
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu'
    }
    
    evaluator = RecommendationEvaluator(config)
    
    # 测试评估函数
    batch_size = 32
    num_items = 1000
    
    # 模拟预测和目标
    predictions = torch.randn(batch_size, num_items)
    targets = torch.zeros(batch_size, num_items)
    
    # 随机设置一些目标物品
    for i in range(batch_size):
        num_targets = np.random.randint(1, 5)
        target_indices = np.random.choice(num_items, num_targets, replace=False)
        targets[i, target_indices] = 1.0
    
    # 评估
    metrics = evaluator.evaluate_batch(predictions, targets)
    
    print("Evaluation Results:")
    for metric_name, value in metrics.items():
        print(f"  {metric_name}: {value:.4f}")


if __name__ == '__main__':
    main()
