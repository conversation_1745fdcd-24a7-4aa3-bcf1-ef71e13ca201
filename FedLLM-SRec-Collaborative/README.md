# FedLLM-SRec-Collaborative: 大小模型协同联邦推荐系统

## 📖 项目概述

基于FELLRec和LLM-SRec两个现有项目，构建一个新的大小模型协同联邦推荐系统。该系统结合了联邦学习的隐私保护特性和大小模型协同的推荐效果优势。

## 🏗️ 系统架构

### 客户端-云端协同架构
```
客户端层:
- 用户设备/客户端
- 用户交互序列处理
- 预训练CF-SRec模型
- 嘉宾用户表示mu

云端层:
- 云端LLM模型
- 优化推荐性能模块
- 整体目标函数(结合推荐损失、索引损失和均匀性损失)
- 计算推荐分数
- 生成推荐列表
```

### 核心组件
1. **客户端模型**: 基于CF-SRec的轻量级序列推荐模型
2. **云端模型**: 基于LLM的大模型推荐系统
3. **联邦聚合**: 基于相似度的智能模型聚合
4. **知识蒸馏**: 大小模型间的知识传递

## 📁 项目结构

```
FedLLM-SRec-Collaborative/
├── README.md                           # 项目说明文档
├── requirements.txt                    # 依赖包列表
├── config/                            # 配置文件目录
│   ├── client_config.yaml            # 客户端配置
│   ├── server_config.yaml            # 服务器配置
│   └── federated_config.yaml         # 联邦学习配置
├── data/                              # 数据目录
│   ├── raw/                          # 原始数据
│   ├── processed/                    # 处理后数据
│   └── federated/                    # 联邦数据分割
├── models/                            # 模型定义
│   ├── client/                       # 客户端模型
│   │   ├── cf_srec.py               # CF-SRec客户端模型
│   │   └── client_trainer.py        # 客户端训练器
│   ├── server/                       # 服务器模型
│   │   ├── llm_srec.py              # LLM服务器模型
│   │   └── server_trainer.py        # 服务器训练器
│   └── federated/                    # 联邦学习模型
│       ├── fed_aggregator.py         # 联邦聚合器
│       └── collaborative_model.py    # 协同模型
├── training/                          # 训练模块
│   ├── federated_trainer.py         # 联邦训练主程序
│   ├── client_training.py           # 客户端训练逻辑
│   └── server_training.py           # 服务器训练逻辑
├── utils/                            # 工具模块
│   ├── data_utils.py                # 数据处理工具
│   ├── fed_utils.py                 # 联邦学习工具
│   ├── evaluation.py               # 评估工具
│   └── visualization.py            # 可视化工具
├── scripts/                          # 运行脚本
│   ├── run_client_training.sh       # 客户端训练脚本
│   ├── run_server_training.sh       # 服务器训练脚本
│   └── run_federated_training.sh    # 联邦训练脚本
└── experiments/                      # 实验结果
    ├── logs/                        # 训练日志
    ├── models/                      # 保存的模型
    └── results/                     # 实验结果
```

## 🔧 核心特性

### 1. 代码复用策略
- **FELLRec复用**: 联邦学习框架、LoRA微调、模型聚合算法
- **LLM-SRec复用**: CF-SRec模型、LLM集成、知识蒸馏机制
- **架构创新**: 客户端-云端协同、大小模型协同

### 2. 技术特点
- **隐私保护**: 用户数据不离开客户端
- **计算效率**: 客户端轻量级模型，云端大模型
- **推荐效果**: 结合序列建模和语言理解能力
- **可扩展性**: 支持多客户端动态加入

### 3. 训练流程
1. **预训练阶段**: CF-SRec模型预训练
2. **联邦训练阶段**: 客户端本地训练 + 服务器聚合
3. **知识蒸馏阶段**: 大小模型知识传递
4. **协同优化阶段**: 端到端优化

## 🚀 快速开始

### 环境配置
```bash
# 创建虚拟环境
conda create -n fedllm-srec python=3.8
conda activate fedllm-srec

# 安装依赖
pip install -r requirements.txt
```

### 数据准备
```bash
# 准备数据集
python scripts/prepare_data.py --dataset Movies_and_TV

# 联邦数据分割
python utils/data_utils.py --split_federated --client_num 5
```

### 模型训练
```bash
# 1. CF-SRec预训练
bash scripts/run_client_training.sh

# 2. 联邦协同训练
bash scripts/run_federated_training.sh

# 3. 模型评估
python utils/evaluation.py --model_path ./experiments/models/best_model
```

## 📊 实验配置

### 基础配置
- **客户端数量**: 3-5个
- **联邦轮数**: 10-20轮
- **学习率**: 客户端1e-4, 服务器1e-5
- **批次大小**: 32-64

### 高级配置
- **聚合策略**: 基于相似度的智能聚合
- **知识蒸馏**: 温度参数4.0, 蒸馏权重0.5
- **LoRA配置**: rank=8, alpha=16

## 📈 预期效果

### 性能指标
- **NDCG@10**: 预期提升10-15%
- **Hit Rate@10**: 预期提升8-12%
- **通信效率**: 减少60%的通信开销
- **隐私保护**: 100%数据本地化

### 技术优势
- 结合联邦学习和大小模型协同的优势
- 保护用户隐私的同时提升推荐效果
- 支持异构客户端和动态参与

## 🔍 待实现功能

1. **架构图对应的代码实现**
2. **客户端-云端通信协议**
3. **大小模型协同机制**
4. **联邦聚合优化算法**
5. **实验评估框架**

## 📝 开发计划

### 阶段1: 基础架构搭建
- [ ] 项目结构创建
- [ ] 基础模型定义
- [ ] 配置文件设计

### 阶段2: 核心功能实现
- [ ] 客户端模型实现
- [ ] 服务器模型实现
- [ ] 联邦聚合算法

### 阶段3: 协同机制实现
- [ ] 知识蒸馏机制
- [ ] 大小模型协同
- [ ] 端到端优化

### 阶段4: 实验验证
- [ ] 数据集准备
- [ ] 实验设计
- [ ] 性能评估

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库: [GitHub链接]
- 邮箱: [联系邮箱]
