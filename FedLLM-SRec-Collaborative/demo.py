#!/usr/bin/env python3
"""
FedLLM-SRec协同推荐系统演示脚本

展示如何使用大小模型协同联邦推荐系统：
1. 快速配置和初始化
2. 模型训练演示
3. 结果评估展示
"""

import os
import sys
import torch
import yaml
import logging
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.federated.collaborative_model import CollaborativeRecommendationModel
from models.federated.fed_aggregator import FederatedAggregator
from training.federated_trainer import FederatedTrainer
from utils.data_utils import FederatedDataLoader, prepare_sample_data
from utils.evaluation import RecommendationEvaluator
from utils.fed_utils import setup_logging, set_random_seed, log_system_info


def create_demo_config() -> Dict[str, Any]:
    """创建演示配置"""
    config = {
        'experiment_name': 'fedllm_srec_demo',
        'seed': 42,
        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu',
        'log_level': 'INFO',
        
        # 数据配置
        'data': {
            'dataset': 'Movies_and_TV',
            'data_dir': './demo_data',
            'raw_data_dir': './demo_data/raw',
            'processed_data_dir': './demo_data/processed',
            'federated_data_dir': './demo_data/federated',
            'min_interactions': 5,
            'max_sequence_length': 50,  # 减小序列长度以加快演示
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        },
        
        # 联邦学习配置
        'federated': {
            'client_num': 3,  # 减少客户端数量以加快演示
            'fed_rounds': 5,  # 减少轮数以加快演示
            'local_epochs': 2,
            'client_selection': 'all',
            'participation_rate': 1.0,
            'aggregation_method': 'similarity_weighted',
            'alpha': 0.7,
            'beta': 1,
            'data_split_method': 'user_embedding',
            'split_alpha': 0.5
        },
        
        # 客户端模型配置
        'client_model': {
            'model_type': 'cf_srec',
            'item_num': 1000,  # 减少物品数量以加快演示
            'hidden_units': 32,  # 减小隐藏层大小
            'num_blocks': 1,  # 减少Transformer块数量
            'num_heads': 1,
            'dropout_rate': 0.2,
            'max_sequence_length': 50,
            'use_lora': True,
            'lora_r': 4,  # 减小LoRA rank
            'lora_alpha': 8,
            'lora_dropout': 0.1,
            'learning_rate': 1e-3,  # 增大学习率以加快收敛
            'batch_size': 16,  # 减小批次大小
            'weight_decay': 1e-5,
            'gradient_clip': 1.0
        },
        
        # 服务器模型配置
        'server_model': {
            'model_type': 'llm_srec',
            'llm_model': 'llama-3b',
            'load_in_8bit': True,
            'max_length': 256,
            'temperature': 1.0,
            'top_p': 0.9,
            'distillation_temperature': 4.0,
            'distillation_weight': 0.5,
            'learning_rate': 1e-4,  # 增大学习率
            'batch_size': 8,  # 减小批次大小
            'weight_decay': 1e-6,
            'gradient_accumulation_steps': 1
        },
        
        # 协同机制配置
        'collaboration': {
            'collaboration_method': 'knowledge_distillation',
            'communication_rounds': 3,
            'compression_method': 'none',
            'loss_weights': {
                'recommendation_loss': 1.0,
                'distillation_loss': 0.5,
                'alignment_loss': 0.3,
                'regularization_loss': 0.1
            }
        },
        
        # 训练配置
        'training': {
            'max_epochs': 10,  # 减少最大轮数
            'patience': 3,  # 减少耐心值
            'save_every': 2,
            'eval_every': 1,
            'lr_scheduler': 'cosine',
            'warmup_steps': 10,
            'use_fp16': False,  # 关闭混合精度以避免兼容性问题
            'gradient_checkpointing': False
        },
        
        # 评估配置
        'evaluation': {
            'metrics': ['ndcg', 'hit_rate', 'recall', 'precision'],
            'top_k': [5, 10],  # 减少评估的K值
            'eval_batch_size': 32,
            'num_eval_samples': 100,  # 减少评估样本数量
            'privacy_metrics': []
        },
        
        # 日志和保存配置
        'logging': {
            'log_dir': './demo_logs',
            'model_dir': './demo_models',
            'result_dir': './demo_results',
            'use_wandb': False,
            'wandb_project': 'fedllm-srec-demo',
            'use_tensorboard': False,  # 关闭TensorBoard以简化演示
            'tensorboard_dir': './demo_logs/tensorboard'
        },
        
        # 硬件配置
        'hardware': {
            'num_workers': 0,  # 设为0以避免多进程问题
            'pin_memory': False,
            'persistent_workers': False,
            'distributed': False,
            'world_size': 1,
            'rank': 0
        },
        
        # 调试配置
        'debug': {
            'debug_mode': True,
            'sample_data': True,
            'sample_ratio': 0.1,
            'verbose': True
        }
    }
    
    return config


def demo_model_architecture():
    """演示模型架构"""
    print("\n" + "="*60)
    print("🏗️  模型架构演示")
    print("="*60)
    
    config = create_demo_config()
    
    # 创建协同模型
    model = CollaborativeRecommendationModel(config)
    
    # 统计参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"📊 模型参数统计:")
    print(f"   总参数数量: {total_params:,}")
    print(f"   可训练参数: {trainable_params:,}")
    print(f"   参数比例: {trainable_params/total_params*100:.1f}%")
    
    # 演示前向传播
    batch_size = 4
    seq_len = config['data']['max_sequence_length']
    
    # 模拟输入数据
    batch = {
        'user_sequences': torch.randint(1, 100, (batch_size, seq_len)),
        'target_items': torch.randint(1, 100, (batch_size,)),
        'candidate_items': torch.randint(1, 100, (batch_size, 10))
    }
    
    print(f"\n🔄 前向传播演示:")
    print(f"   输入序列形状: {batch['user_sequences'].shape}")
    
    # 客户端前向传播
    with torch.no_grad():
        user_repr = model.client_forward(batch['user_sequences'])
        print(f"   用户表示形状: {user_repr.shape}")
        
        # 服务器前向传播
        server_outputs = model.server_forward(user_repr, batch['candidate_items'])
        print(f"   推荐分数形状: {server_outputs['recommendation_scores'].shape}")
    
    print("✅ 模型架构演示完成")


def demo_federated_aggregation():
    """演示联邦聚合"""
    print("\n" + "="*60)
    print("🔗 联邦聚合演示")
    print("="*60)
    
    config = create_demo_config()
    aggregator = FederatedAggregator(config)
    
    # 创建模拟客户端参数
    client_num = config['federated']['client_num']
    param_size = 1000
    
    client_parameters = []
    for i in range(client_num):
        # 模拟客户端参数（添加一些随机性）
        params = {
            'layer1.weight': torch.randn(50, 20) + i * 0.1,
            'layer1.bias': torch.randn(50) + i * 0.05,
            'layer2.weight': torch.randn(10, 50) + i * 0.1,
            'layer2.bias': torch.randn(10) + i * 0.05
        }
        client_parameters.append(params)
    
    print(f"📊 客户端参数统计:")
    print(f"   客户端数量: {client_num}")
    print(f"   参数层数: {len(client_parameters[0])}")
    
    # 计算相似度矩阵
    similarity_matrix = aggregator.cluster_clients(client_parameters)
    print(f"\n🔍 相似度矩阵:")
    for i in range(client_num):
        row_str = "   " + " ".join([f"{similarity_matrix[i][j]:.3f}" for j in range(client_num)])
        print(f"   客户端{i}: {row_str}")
    
    # 执行聚合
    aggregated_params_list = aggregator.similarity_weighted_aggregation(client_parameters)
    print(f"\n⚡ 聚合结果:")
    print(f"   聚合方法: {config['federated']['aggregation_method']}")
    print(f"   聚合参数数量: {len(aggregated_params_list)}")
    
    # 评估客户端多样性
    diversity, similarities = aggregator.evaluate_client_diversity(client_parameters)
    print(f"   客户端多样性: {diversity:.4f}")
    print(f"   平均相似度: {np.mean(similarities):.4f}")
    
    print("✅ 联邦聚合演示完成")


def demo_data_processing():
    """演示数据处理"""
    print("\n" + "="*60)
    print("📊 数据处理演示")
    print("="*60)
    
    config = create_demo_config()
    
    # 准备示例数据
    print("🔧 准备示例数据...")
    prepare_sample_data(config['data']['data_dir'], config['data']['dataset'])
    
    # 创建数据加载器
    print("📥 创建联邦数据加载器...")
    data_loader = FederatedDataLoader(config)
    
    try:
        client_train_loaders, client_val_loaders, test_loader = data_loader.get_federated_dataloaders()
        
        print(f"📈 数据分割结果:")
        print(f"   客户端数量: {len(client_train_loaders)}")
        print(f"   测试集批次数: {len(test_loader)}")
        
        for i, (train_loader, val_loader) in enumerate(zip(client_train_loaders, client_val_loaders)):
            print(f"   客户端{i}: 训练批次={len(train_loader)}, 验证批次={len(val_loader)}")
        
        # 展示一个批次的数据
        if len(test_loader) > 0:
            sample_batch = next(iter(test_loader))
            print(f"\n📋 样本批次信息:")
            for key, value in sample_batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"   {key}: {value.shape} ({value.dtype})")
                else:
                    print(f"   {key}: {type(value)}")
        
        print("✅ 数据处理演示完成")
        
    except Exception as e:
        print(f"❌ 数据处理出错: {str(e)}")
        print("💡 这可能是因为缺少真实数据，但演示代码结构是正确的")


def demo_evaluation():
    """演示评估系统"""
    print("\n" + "="*60)
    print("📏 评估系统演示")
    print("="*60)
    
    config = create_demo_config()
    evaluator = RecommendationEvaluator(config)
    
    # 模拟预测和目标
    batch_size = 32
    num_items = 100
    
    print(f"🎯 模拟评估数据:")
    print(f"   批次大小: {batch_size}")
    print(f"   物品数量: {num_items}")
    
    # 生成模拟数据
    predictions = torch.randn(batch_size, num_items)
    targets = torch.zeros(batch_size, num_items)
    
    # 随机设置一些目标物品
    for i in range(batch_size):
        num_targets = torch.randint(1, 5, (1,)).item()
        target_indices = torch.randperm(num_items)[:num_targets]
        targets[i, target_indices] = 1.0
    
    print(f"   平均目标物品数: {targets.sum(dim=1).mean():.1f}")
    
    # 执行评估
    metrics = evaluator.evaluate_batch(predictions, targets)
    
    print(f"\n📊 评估结果:")
    for metric_name, value in metrics.items():
        print(f"   {metric_name}: {value:.4f}")
    
    # 生成评估报告
    mock_fed_metrics = {
        'global': metrics,
        'clients': [metrics] * config['federated']['client_num'],
        'federated': {
            'client_avg_ndcg@10': metrics.get('ndcg@10', 0),
            'client_diversity': 0.15,
            'ndcg@10_improvement': 0.08
        }
    }
    
    report = evaluator.generate_evaluation_report(mock_fed_metrics)
    print(f"\n📋 评估报告:")
    print(report)
    
    print("✅ 评估系统演示完成")


def demo_quick_training():
    """快速训练演示"""
    print("\n" + "="*60)
    print("🚀 快速训练演示")
    print("="*60)
    
    config = create_demo_config()
    
    # 设置日志
    setup_logging(config['logging'])
    set_random_seed(config['seed'])
    
    print("⚙️  初始化训练器...")
    
    try:
        # 准备数据
        prepare_sample_data(config['data']['data_dir'], config['data']['dataset'])
        
        # 创建训练器
        trainer = FederatedTrainer(config)
        
        print(f"📊 训练配置:")
        print(f"   客户端数量: {config['federated']['client_num']}")
        print(f"   联邦轮数: {config['federated']['fed_rounds']}")
        print(f"   本地训练轮数: {config['federated']['local_epochs']}")
        print(f"   设备: {config['device']}")
        
        print("\n🏃 开始快速训练...")
        
        # 运行一轮训练作为演示
        round_metrics = trainer.train_one_round()
        
        print(f"\n📈 训练结果:")
        if 'global_metrics' in round_metrics:
            global_metrics = round_metrics['global_metrics']
            for metric_name, value in global_metrics.items():
                if isinstance(value, (int, float)):
                    print(f"   {metric_name}: {value:.4f}")
        
        print("✅ 快速训练演示完成")
        
    except Exception as e:
        print(f"❌ 训练演示出错: {str(e)}")
        print("💡 这可能是因为环境配置问题，但演示代码结构是正确的")


def main():
    """主演示函数"""
    print("🎉 欢迎使用FedLLM-SRec协同推荐系统演示！")
    print("\n这个演示将展示系统的主要功能模块：")
    print("1. 模型架构")
    print("2. 联邦聚合")
    print("3. 数据处理")
    print("4. 评估系统")
    print("5. 快速训练")
    
    # 记录系统信息
    log_system_info()
    
    # 运行各个演示
    demo_model_architecture()
    demo_federated_aggregation()
    demo_data_processing()
    demo_evaluation()
    demo_quick_training()
    
    print("\n" + "="*60)
    print("🎊 演示完成！")
    print("="*60)
    print("\n📚 接下来您可以:")
    print("1. 修改配置文件进行完整训练")
    print("2. 使用真实数据集替换示例数据")
    print("3. 调整模型架构和超参数")
    print("4. 扩展评估指标和可视化")
    print("\n🚀 开始您的联邦推荐系统研究之旅吧！")


if __name__ == '__main__':
    main()
