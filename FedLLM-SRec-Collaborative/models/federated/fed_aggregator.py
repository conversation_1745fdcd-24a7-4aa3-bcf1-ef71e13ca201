"""
联邦聚合器 - 基于FELLRec的智能聚合算法

实现基于相似度的智能模型聚合，支持：
1. 客户端模型相似度计算
2. 基于相似度的权重聚合
3. 动态权重调整
4. 隐私保护聚合
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from copy import deepcopy

logger = logging.getLogger(__name__)


class FederatedAggregator:
    """
    联邦聚合器
    
    基于FELLRec的聚合算法，实现智能模型聚合：
    1. 计算客户端模型间的相似度矩阵
    2. 基于相似度进行加权聚合
    3. 支持动态权重调整和隐私保护
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.aggregation_method = config['federated'].get('aggregation_method', 'similarity_weighted')
        self.alpha = config['federated'].get('alpha', 0.7)
        self.beta = config['federated'].get('beta', 1)
        
        logger.info(f"FederatedAggregator initialized with method: {self.aggregation_method}")
    
    def extract_model_parameters(self, model: nn.Module) -> Dict[str, torch.Tensor]:
        """
        提取模型的可训练参数
        
        Args:
            model: PyTorch模型
            
        Returns:
            parameters: 参数字典
        """
        return {name: param.clone().detach() for name, param in model.named_parameters() if param.requires_grad}
    
    def flatten_parameters(self, parameters: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        将参数字典展平为一维向量
        
        Args:
            parameters: 参数字典
            
        Returns:
            flattened: 展平的参数向量
        """
        flattened_params = []
        for param in parameters.values():
            flattened_params.append(param.view(-1).cpu())
        return torch.cat(flattened_params)
    
    def cluster_clients(self, client_parameters: List[Dict[str, torch.Tensor]]) -> np.ndarray:
        """
        计算客户端模型间的相似度矩阵
        
        基于FELLRec的cluster_clients函数实现
        
        Args:
            client_parameters: 客户端参数列表
            
        Returns:
            similarity_matrix: 归一化的相似度矩阵
        """
        # 将参数展平为一维向量
        param_vectors = []
        for params in client_parameters:
            flattened = self.flatten_parameters(params)
            param_vectors.append(flattened.numpy())
        
        # 计算余弦相似度矩阵
        params_matrix = np.vstack(param_vectors)
        similarity_matrix = cosine_similarity(params_matrix)
        
        # 归一化到[0, 1]范围
        normalized_matrix = (similarity_matrix + 1) / 2
        
        logger.info(f"Computed similarity matrix for {len(client_parameters)} clients")
        logger.debug(f"Similarity matrix shape: {normalized_matrix.shape}")
        
        return normalized_matrix
    
    def get_aggregate_weights(self, client_index: int, similarity_matrix: np.ndarray, 
                            client_parameters: List[Dict[str, torch.Tensor]], 
                            weight: float, beta: int) -> Dict[str, torch.Tensor]:
        """
        基于相似度的权重聚合
        
        基于FELLRec的get_aggregate_lora_weight函数实现
        
        Args:
            client_index: 当前客户端索引
            similarity_matrix: 相似度矩阵
            client_parameters: 客户端参数列表
            weight: 聚合权重参数
            beta: 权重调节参数
            
        Returns:
            aggregated_params: 聚合后的参数
        """
        # 调整相似度权重
        adjusted_similarities = similarity_matrix[client_index].copy()
        for i in range(len(adjusted_similarities)):
            if i != client_index and beta != 1:
                adjusted_similarities[i] = adjusted_similarities[i] * weight
        
        # 归一化权重
        total_weight = np.sum(adjusted_similarities)
        if total_weight == 0:
            logger.warning(f"Total weight is 0 for client {client_index}, using uniform weights")
            adjusted_similarities = np.ones_like(adjusted_similarities) / len(adjusted_similarities)
            total_weight = 1.0
        else:
            adjusted_similarities = adjusted_similarities / total_weight
        
        # 加权聚合参数
        aggregated_params = {}
        param_names = client_parameters[0].keys()
        
        with torch.no_grad():
            for param_name in param_names:
                # 计算加权平均
                weighted_param = torch.zeros_like(client_parameters[client_index][param_name])
                for i, params in enumerate(client_parameters):
                    weighted_param += params[param_name] * adjusted_similarities[i]
                
                aggregated_params[param_name] = weighted_param
        
        logger.debug(f"Aggregated parameters for client {client_index} using weights: {adjusted_similarities}")
        
        return aggregated_params
    
    def federated_averaging(self, client_parameters: List[Dict[str, torch.Tensor]], 
                          weights: Optional[List[float]] = None) -> Dict[str, torch.Tensor]:
        """
        标准联邦平均算法
        
        Args:
            client_parameters: 客户端参数列表
            weights: 客户端权重（可选）
            
        Returns:
            averaged_params: 平均后的参数
        """
        if weights is None:
            weights = [1.0 / len(client_parameters)] * len(client_parameters)
        
        # 归一化权重
        total_weight = sum(weights)
        weights = [w / total_weight for w in weights]
        
        # 初始化聚合参数
        averaged_params = {}
        param_names = client_parameters[0].keys()
        
        for param_name in param_names:
            # 加权平均
            weighted_sum = torch.zeros_like(client_parameters[0][param_name])
            for params, weight in zip(client_parameters, weights):
                weighted_sum += params[param_name] * weight
            
            averaged_params[param_name] = weighted_sum
        
        logger.info(f"Federated averaging completed with weights: {weights}")
        
        return averaged_params
    
    def similarity_weighted_aggregation(self, client_parameters: List[Dict[str, torch.Tensor]]) -> List[Dict[str, torch.Tensor]]:
        """
        基于相似度的加权聚合
        
        为每个客户端生成个性化的聚合参数
        
        Args:
            client_parameters: 客户端参数列表
            
        Returns:
            aggregated_params_list: 每个客户端的聚合参数列表
        """
        # 计算相似度矩阵
        similarity_matrix = self.cluster_clients(client_parameters)
        
        # 为每个客户端生成聚合参数
        aggregated_params_list = []
        for client_idx in range(len(client_parameters)):
            aggregated_params = self.get_aggregate_weights(
                client_idx, similarity_matrix, client_parameters, self.alpha, self.beta
            )
            aggregated_params_list.append(aggregated_params)
        
        logger.info(f"Similarity-weighted aggregation completed for {len(client_parameters)} clients")
        
        return aggregated_params_list
    
    def adaptive_aggregation(self, client_parameters: List[Dict[str, torch.Tensor]], 
                           performance_scores: Optional[List[float]] = None) -> Dict[str, torch.Tensor]:
        """
        自适应聚合算法
        
        根据客户端性能动态调整聚合权重
        
        Args:
            client_parameters: 客户端参数列表
            performance_scores: 客户端性能分数（可选）
            
        Returns:
            aggregated_params: 聚合后的参数
        """
        if performance_scores is None:
            # 如果没有性能分数，使用均匀权重
            weights = [1.0 / len(client_parameters)] * len(client_parameters)
        else:
            # 基于性能分数计算权重
            total_score = sum(performance_scores)
            if total_score == 0:
                weights = [1.0 / len(client_parameters)] * len(client_parameters)
            else:
                weights = [score / total_score for score in performance_scores]
        
        return self.federated_averaging(client_parameters, weights)
    
    def privacy_preserving_aggregation(self, client_parameters: List[Dict[str, torch.Tensor]], 
                                     noise_scale: float = 0.01) -> Dict[str, torch.Tensor]:
        """
        隐私保护聚合
        
        在聚合过程中添加差分隐私噪声
        
        Args:
            client_parameters: 客户端参数列表
            noise_scale: 噪声尺度
            
        Returns:
            noisy_params: 添加噪声后的聚合参数
        """
        # 先进行标准聚合
        aggregated_params = self.federated_averaging(client_parameters)
        
        # 添加高斯噪声
        noisy_params = {}
        for param_name, param_value in aggregated_params.items():
            noise = torch.normal(0, noise_scale, size=param_value.shape).to(param_value.device)
            noisy_params[param_name] = param_value + noise
        
        logger.info(f"Privacy-preserving aggregation completed with noise scale: {noise_scale}")
        
        return noisy_params
    
    def aggregate(self, client_parameters: List[Dict[str, torch.Tensor]], 
                 performance_scores: Optional[List[float]] = None, 
                 **kwargs) -> Any:
        """
        主聚合函数
        
        根据配置选择合适的聚合方法
        
        Args:
            client_parameters: 客户端参数列表
            performance_scores: 客户端性能分数（可选）
            **kwargs: 其他参数
            
        Returns:
            aggregated_result: 聚合结果
        """
        if self.aggregation_method == 'fedavg':
            return self.federated_averaging(client_parameters)
        
        elif self.aggregation_method == 'similarity_weighted':
            return self.similarity_weighted_aggregation(client_parameters)
        
        elif self.aggregation_method == 'adaptive':
            return self.adaptive_aggregation(client_parameters, performance_scores)
        
        elif self.aggregation_method == 'privacy_preserving':
            noise_scale = kwargs.get('noise_scale', 0.01)
            return self.privacy_preserving_aggregation(client_parameters, noise_scale)
        
        else:
            logger.warning(f"Unknown aggregation method: {self.aggregation_method}, using fedavg")
            return self.federated_averaging(client_parameters)
    
    def evaluate_client_diversity(self, client_parameters: List[Dict[str, torch.Tensor]]) -> Tuple[float, List[float]]:
        """
        评估客户端多样性
        
        Args:
            client_parameters: 客户端参数列表
            
        Returns:
            diversity: 多样性分数
            similarities: 相似度列表
        """
        similarities = []
        num_clients = len(client_parameters)
        
        for i in range(num_clients):
            for j in range(i + 1, num_clients):
                # 计算两个客户端的相似度
                param1_flat = self.flatten_parameters(client_parameters[i])
                param2_flat = self.flatten_parameters(client_parameters[j])
                
                similarity = torch.cosine_similarity(param1_flat.unsqueeze(0), param2_flat.unsqueeze(0)).item()
                similarities.append(similarity)
        
        # 多样性 = 1 - 平均相似度
        avg_similarity = np.mean(similarities)
        diversity = 1.0 - avg_similarity
        
        logger.info(f"Client diversity: {diversity:.4f}, Average similarity: {avg_similarity:.4f}")
        
        return diversity, similarities
    
    def update_model_parameters(self, model: nn.Module, new_parameters: Dict[str, torch.Tensor]):
        """
        更新模型参数
        
        Args:
            model: 要更新的模型
            new_parameters: 新的参数字典
        """
        with torch.no_grad():
            for name, param in model.named_parameters():
                if name in new_parameters and param.requires_grad:
                    param.copy_(new_parameters[name])
        
        logger.debug(f"Updated model parameters: {list(new_parameters.keys())}")
    
    def get_aggregation_statistics(self, client_parameters: List[Dict[str, torch.Tensor]]) -> Dict[str, Any]:
        """
        获取聚合统计信息
        
        Args:
            client_parameters: 客户端参数列表
            
        Returns:
            stats: 统计信息字典
        """
        diversity, similarities = self.evaluate_client_diversity(client_parameters)
        similarity_matrix = self.cluster_clients(client_parameters)
        
        stats = {
            'num_clients': len(client_parameters),
            'diversity': diversity,
            'avg_similarity': np.mean(similarities),
            'max_similarity': np.max(similarities),
            'min_similarity': np.min(similarities),
            'similarity_matrix': similarity_matrix.tolist(),
            'aggregation_method': self.aggregation_method,
            'alpha': self.alpha,
            'beta': self.beta
        }
        
        return stats
