"""
FedLLM-SRec协同推荐系统主模型

基于您提供的架构图实现的大小模型协同联邦推荐系统：
- 客户端：用户设备 → 客户端 → 预训练CF-SRec模型 → 嘉宾用户表示mu
- 云端：云端LLM模型 → 优化推荐性能模块 → 整体目标函数 → 推荐结果
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model, PeftModel

logger = logging.getLogger(__name__)


class CollaborativeRecommendationModel(nn.Module):
    """
    大小模型协同联邦推荐系统主模型
    
    架构特点：
    1. 客户端：轻量级CF-SRec模型处理用户交互序列
    2. 云端：LLM模型进行推荐性能优化
    3. 协同机制：知识蒸馏和特征对齐
    4. 联邦聚合：基于相似度的智能聚合
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.device = config.get('device', 'cuda:0')
        
        # 初始化客户端模型组件
        self._init_client_components()
        
        # 初始化服务器模型组件
        self._init_server_components()
        
        # 初始化协同机制
        self._init_collaboration_components()
        
        # 初始化损失函数
        self._init_loss_functions()
        
        logger.info("CollaborativeRecommendationModel initialized successfully")
    
    def _init_client_components(self):
        """初始化客户端模型组件"""
        client_config = self.config['client_model']
        
        # CF-SRec客户端模型参数
        self.item_num = client_config.get('item_num', 10000)
        self.hidden_units = client_config.get('hidden_units', 64)
        self.num_blocks = client_config.get('num_blocks', 2)
        self.num_heads = client_config.get('num_heads', 1)
        self.dropout_rate = client_config.get('dropout_rate', 0.2)
        self.maxlen = client_config.get('max_sequence_length', 128)
        
        # 物品嵌入层
        self.item_emb = nn.Embedding(self.item_num + 1, self.hidden_units, padding_idx=0)
        self.pos_emb = nn.Embedding(self.maxlen, self.hidden_units)
        
        # Transformer块
        self.attention_layernorms = nn.ModuleList()
        self.attention_layers = nn.ModuleList()
        self.forward_layernorms = nn.ModuleList()
        self.forward_layers = nn.ModuleList()
        
        for _ in range(self.num_blocks):
            self.attention_layernorms.append(nn.LayerNorm(self.hidden_units, eps=1e-8))
            self.attention_layers.append(
                nn.MultiheadAttention(self.hidden_units, self.num_heads, dropout=self.dropout_rate)
            )
            self.forward_layernorms.append(nn.LayerNorm(self.hidden_units, eps=1e-8))
            self.forward_layers.append(
                nn.Sequential(
                    nn.Linear(self.hidden_units, self.hidden_units * 4),
                    nn.GELU(),
                    nn.Dropout(self.dropout_rate),
                    nn.Linear(self.hidden_units * 4, self.hidden_units),
                    nn.Dropout(self.dropout_rate)
                )
            )
        
        self.last_layernorm = nn.LayerNorm(self.hidden_units, eps=1e-8)
        
        logger.info(f"Client CF-SRec model initialized with {self.hidden_units} hidden units")
    
    def _init_server_components(self):
        """初始化服务器LLM组件"""
        server_config = self.config['server_model']
        
        # LLM模型配置
        self.llm_model_name = server_config.get('llm_model', 'llama-3b')
        self.load_in_8bit = server_config.get('load_in_8bit', True)
        
        # 这里暂时用占位符，实际使用时需要加载真实的LLM模型
        self.llm_hidden_size = 2048  # LLaMA-3B的隐藏层大小
        
        # 推荐性能优化模块
        self.recommendation_optimizer = nn.Sequential(
            nn.Linear(self.hidden_units, self.llm_hidden_size),
            nn.LayerNorm(self.llm_hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(self.llm_hidden_size, self.llm_hidden_size)
        )
        
        # 整体目标函数模块
        self.objective_function = nn.ModuleDict({
            'recommendation_head': nn.Linear(self.llm_hidden_size, self.item_num),
            'alignment_head': nn.Linear(self.llm_hidden_size, self.hidden_units),
            'regularization_head': nn.Linear(self.llm_hidden_size, 1)
        })
        
        logger.info(f"Server LLM components initialized for {self.llm_model_name}")
    
    def _init_collaboration_components(self):
        """初始化协同机制组件"""
        collab_config = self.config['collaboration']
        
        # 知识蒸馏组件
        self.distillation_temperature = collab_config.get('distillation_temperature', 4.0)
        
        # 特征对齐组件
        self.feature_alignment = nn.Sequential(
            nn.Linear(self.hidden_units, self.llm_hidden_size // 2),
            nn.ReLU(),
            nn.Linear(self.llm_hidden_size // 2, self.llm_hidden_size)
        )
        
        # 用户表示投影层（mu生成）
        self.user_representation_proj = nn.Sequential(
            nn.Linear(self.hidden_units, self.hidden_units * 2),
            nn.LayerNorm(self.hidden_units * 2),
            nn.GELU(),
            nn.Linear(self.hidden_units * 2, self.hidden_units)
        )
        
        logger.info("Collaboration components initialized")
    
    def _init_loss_functions(self):
        """初始化损失函数"""
        # 推荐损失
        self.recommendation_loss = nn.CrossEntropyLoss()
        
        # 知识蒸馏损失
        self.distillation_loss = nn.KLDivLoss(reduction='batchmean')
        
        # 特征对齐损失
        self.alignment_loss = nn.MSELoss()
        
        # 正则化损失
        self.regularization_loss = nn.L1Loss()
        
        logger.info("Loss functions initialized")
    
    def client_forward(self, user_sequences: torch.Tensor) -> torch.Tensor:
        """
        客户端前向传播：用户交互序列 → CF-SRec模型 → 用户表示
        
        Args:
            user_sequences: 用户交互序列 [batch_size, seq_len]
            
        Returns:
            user_representations: 用户表示mu [batch_size, hidden_units]
        """
        batch_size, seq_len = user_sequences.shape
        
        # 物品嵌入
        item_embs = self.item_emb(user_sequences)  # [batch_size, seq_len, hidden_units]
        
        # 位置嵌入
        positions = torch.arange(seq_len, device=self.device).unsqueeze(0).repeat(batch_size, 1)
        pos_embs = self.pos_emb(positions)
        
        # 输入嵌入
        seqs = item_embs + pos_embs
        seqs = F.dropout(seqs, p=self.dropout_rate, training=self.training)
        
        # 注意力掩码
        timeline_mask = (user_sequences == 0)
        seqs *= ~timeline_mask.unsqueeze(-1)
        
        # Transformer块
        for i in range(self.num_blocks):
            # 自注意力
            seqs_norm = self.attention_layernorms[i](seqs)
            seqs_transposed = seqs_norm.transpose(0, 1)  # [seq_len, batch_size, hidden_units]
            
            attn_output, _ = self.attention_layers[i](
                seqs_transposed, seqs_transposed, seqs_transposed,
                key_padding_mask=timeline_mask
            )
            seqs = seqs + attn_output.transpose(0, 1)
            
            # 前馈网络
            seqs_norm = self.forward_layernorms[i](seqs)
            seqs = seqs + self.forward_layers[i](seqs_norm)
            
            # 掩码
            seqs *= ~timeline_mask.unsqueeze(-1)
        
        # 最终层归一化
        seqs = self.last_layernorm(seqs)
        
        # 获取用户表示（序列最后一个有效位置）
        # 找到每个序列的最后一个非零位置
        seq_lengths = (~timeline_mask).sum(dim=1) - 1  # [batch_size]
        seq_lengths = torch.clamp(seq_lengths, min=0)
        
        user_representations = seqs[torch.arange(batch_size), seq_lengths]  # [batch_size, hidden_units]
        
        # 生成嘉宾用户表示mu
        mu = self.user_representation_proj(user_representations)
        
        return mu
    
    def server_forward(self, user_representations: torch.Tensor, 
                      candidate_items: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        服务器前向传播：用户表示 → LLM优化 → 推荐结果
        
        Args:
            user_representations: 客户端生成的用户表示mu [batch_size, hidden_units]
            candidate_items: 候选物品 [batch_size, num_candidates]
            
        Returns:
            outputs: 包含推荐分数、对齐特征等的字典
        """
        batch_size = user_representations.shape[0]
        
        # 推荐性能优化模块
        optimized_features = self.recommendation_optimizer(user_representations)  # [batch_size, llm_hidden_size]
        
        # 整体目标函数计算
        outputs = {}
        
        # 1. 推荐损失计算
        recommendation_scores = self.objective_function['recommendation_head'](optimized_features)
        outputs['recommendation_scores'] = recommendation_scores
        
        # 2. 索引损失和均匀性损失（通过对齐头计算）
        alignment_features = self.objective_function['alignment_head'](optimized_features)
        outputs['alignment_features'] = alignment_features
        
        # 3. 正则化项
        regularization_scores = self.objective_function['regularization_head'](optimized_features)
        outputs['regularization_scores'] = regularization_scores
        
        # 4. 生成推荐列表（Top-K）
        if candidate_items is not None:
            candidate_scores = torch.gather(recommendation_scores, 1, candidate_items)
            outputs['candidate_scores'] = candidate_scores
        
        return outputs
    
    def forward(self, batch: Dict[str, torch.Tensor], mode: str = 'train') -> Dict[str, torch.Tensor]:
        """
        完整的前向传播流程
        
        Args:
            batch: 包含用户序列、候选物品等的批次数据
            mode: 运行模式 ('train', 'eval', 'client_only', 'server_only')
            
        Returns:
            outputs: 模型输出结果
        """
        user_sequences = batch['user_sequences']  # [batch_size, seq_len]
        
        outputs = {}
        
        if mode in ['train', 'eval', 'client_only']:
            # 客户端前向传播
            user_representations = self.client_forward(user_sequences)
            outputs['user_representations'] = user_representations
        
        if mode in ['train', 'eval', 'server_only']:
            # 如果是server_only模式，使用预计算的用户表示
            if mode == 'server_only':
                user_representations = batch['user_representations']
            
            # 服务器前向传播
            candidate_items = batch.get('candidate_items', None)
            server_outputs = self.server_forward(user_representations, candidate_items)
            outputs.update(server_outputs)
        
        return outputs
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], 
                    targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        计算综合损失函数
        
        Args:
            outputs: 模型输出
            targets: 目标标签
            
        Returns:
            losses: 各项损失的字典
        """
        losses = {}
        loss_weights = self.config['collaboration']['loss_weights']
        
        # 1. 推荐损失
        if 'recommendation_scores' in outputs and 'target_items' in targets:
            rec_loss = self.recommendation_loss(
                outputs['recommendation_scores'], 
                targets['target_items']
            )
            losses['recommendation_loss'] = rec_loss * loss_weights['recommendation_loss']
        
        # 2. 知识蒸馏损失
        if 'client_features' in targets and 'alignment_features' in outputs:
            # 使用温度缩放的知识蒸馏
            client_soft = F.softmax(targets['client_features'] / self.distillation_temperature, dim=-1)
            server_log_soft = F.log_softmax(outputs['alignment_features'] / self.distillation_temperature, dim=-1)
            
            distill_loss = self.distillation_loss(server_log_soft, client_soft)
            distill_loss *= (self.distillation_temperature ** 2)
            losses['distillation_loss'] = distill_loss * loss_weights['distillation_loss']
        
        # 3. 特征对齐损失
        if 'user_representations' in outputs and 'alignment_features' in outputs:
            align_loss = self.alignment_loss(
                outputs['alignment_features'],
                outputs['user_representations']
            )
            losses['alignment_loss'] = align_loss * loss_weights['alignment_loss']
        
        # 4. 正则化损失
        if 'regularization_scores' in outputs:
            reg_loss = torch.mean(torch.abs(outputs['regularization_scores']))
            losses['regularization_loss'] = reg_loss * loss_weights['regularization_loss']
        
        # 总损失
        total_loss = sum(losses.values())
        losses['total_loss'] = total_loss
        
        return losses
    
    def get_user_representation(self, user_sequences: torch.Tensor) -> torch.Tensor:
        """获取用户表示（用于联邦聚合）"""
        with torch.no_grad():
            return self.client_forward(user_sequences)
    
    def generate_recommendations(self, user_representations: torch.Tensor, 
                               top_k: int = 10) -> torch.Tensor:
        """生成推荐列表"""
        with torch.no_grad():
            outputs = self.server_forward(user_representations, None)
            recommendation_scores = outputs['recommendation_scores']
            
            # 获取Top-K推荐
            _, top_items = torch.topk(recommendation_scores, top_k, dim=-1)
            return top_items
