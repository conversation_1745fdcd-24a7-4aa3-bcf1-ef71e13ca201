# ==================== 核心深度学习框架 ====================
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
datasets>=2.12.0

# ==================== 联邦学习和模型微调 ====================
peft>=0.4.0
accelerate>=0.20.0
bitsandbytes>=0.39.0

# ==================== 大语言模型支持 ====================
# LLaMA模型支持
llama-cpp-python>=0.1.78
sentencepiece>=0.1.99

# ==================== 数据处理和科学计算 ====================
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# ==================== 推荐系统专用库 ====================
# 序列推荐和嵌入
sentence-transformers>=2.2.0
faiss-cpu>=1.7.4
implicit>=0.6.2
surprise>=1.1.3

# ==================== 联邦学习框架 ====================
# 联邦学习相关
flower>=1.4.0
fedml>=0.8.7

# ==================== 配置和日志管理 ====================
pyyaml>=6.0
wandb>=0.15.0
tensorboard>=2.13.0
tqdm>=4.65.0
loguru>=0.7.0

# ==================== 评估和可视化 ====================
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# ==================== 数据格式和工具库 ====================
jsonlines>=3.1.0
h5py>=3.8.0
pickle5>=0.0.11

# ==================== 网络和通信 ====================
requests>=2.31.0
websockets>=11.0.0
grpcio>=1.54.0

# ==================== 知识蒸馏和模型压缩 ====================
# 模型压缩工具
torch-pruning>=1.2.0

# ==================== 隐私保护 ====================
# 差分隐私
opacus>=1.4.0
crypten>=0.4.1

# ==================== 开发和测试工具 ====================
pytest>=7.4.0
pytest-cov>=4.1.0
black>=23.3.0
flake8>=6.0.0
isort>=5.12.0

# ==================== 性能监控 ====================
psutil>=5.9.0
memory-profiler>=0.60.0
py-spy>=0.3.14

# ==================== 可选依赖 ====================
# GPU加速（可选）
# nvidia-ml-py>=12.535.77

# HPU支持（可选）
# habana-frameworks-torch

# 分布式训练（可选）
# torch-distributed-elastic>=0.2.2
