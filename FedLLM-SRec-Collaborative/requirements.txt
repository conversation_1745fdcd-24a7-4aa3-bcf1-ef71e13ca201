# 基础深度学习框架
torch>=1.12.0
torchvision>=0.13.0
transformers>=4.21.0
datasets>=2.4.0

# 联邦学习和模型微调
peft>=0.4.0
accelerate>=0.20.0
bitsandbytes>=0.39.0

# 数据处理和科学计算
numpy>=1.21.0
pandas>=1.4.0
scipy>=1.8.0
scikit-learn>=1.1.0

# 推荐系统相关
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0

# 配置和日志
pyyaml>=6.0
wandb>=0.13.0
tensorboard>=2.9.0
tqdm>=4.64.0

# 评估和可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.9.0

# 工具库
jsonlines>=3.1.0
pickle5>=0.0.11
gzip>=1.0
argparse>=1.4.0

# 分布式训练
torch-distributed>=0.1.0

# 可选：HPU支持
# habana-frameworks-torch

# 开发工具
pytest>=7.1.0
black>=22.6.0
flake8>=5.0.0
