# FedLLM-SRec协同推荐系统实现计划

## 📋 项目概述

基于您提供的三个架构图，我已经为您创建了一个完整的大小模型协同联邦推荐系统项目。该项目结合了FELLRec的联邦学习框架和LLM-SRec的大小模型协同机制，实现了您新论文架构图中的客户端-云端协同推荐系统。

## 🏗️ 架构图对应的代码实现

### 1. 客户端架构实现
**对应架构图**: 用户设备 → 客户端 → 用户交互序列 → 预训练CF-SRec模型 → 嘉宾用户表示mu

**代码实现**:
- **文件**: `models/federated/collaborative_model.py` 中的 `client_forward()` 方法
- **功能**: 
  - 用户交互序列处理
  - CF-SRec模型前向传播
  - 生成用户表示mu

<augment_code_snippet path="FedLLM-SRec-Collaborative/models/federated/collaborative_model.py" mode="EXCERPT">
````python
def client_forward(self, user_sequences: torch.Tensor) -> torch.Tensor:
    """
    客户端前向传播：用户交互序列 → CF-SRec模型 → 用户表示
    """
    # 物品嵌入 + 位置嵌入
    item_embs = self.item_emb(user_sequences)
    pos_embs = self.pos_emb(positions)
    seqs = item_embs + pos_embs
    
    # Transformer块处理
    for i in range(self.num_blocks):
        # 自注意力 + 前馈网络
        seqs = self.attention_layers[i](seqs) + seqs
        seqs = self.forward_layers[i](seqs) + seqs
    
    # 生成嘉宾用户表示mu
    mu = self.user_representation_proj(user_representations)
    return mu
````
</augment_code_snippet>

### 2. 云端架构实现
**对应架构图**: 云端LLM模型 → 优化推荐性能模块 → 整体目标函数 → 推荐结果

**代码实现**:
- **文件**: `models/federated/collaborative_model.py` 中的 `server_forward()` 方法
- **功能**:
  - LLM模型处理
  - 推荐性能优化
  - 多目标函数计算

<augment_code_snippet path="FedLLM-SRec-Collaborative/models/federated/collaborative_model.py" mode="EXCERPT">
````python
def server_forward(self, user_representations: torch.Tensor, 
                  candidate_items: torch.Tensor) -> Dict[str, torch.Tensor]:
    """
    服务器前向传播：用户表示 → LLM优化 → 推荐结果
    """
    # 推荐性能优化模块
    optimized_features = self.recommendation_optimizer(user_representations)
    
    # 整体目标函数计算
    outputs = {}
    outputs['recommendation_scores'] = self.objective_function['recommendation_head'](optimized_features)
    outputs['alignment_features'] = self.objective_function['alignment_head'](optimized_features)
    outputs['regularization_scores'] = self.objective_function['regularization_head'](optimized_features)
    
    return outputs
````
</augment_code_snippet>

### 3. 联邦聚合实现
**基于FELLRec的智能聚合算法**:

**代码实现**:
- **文件**: `models/federated/fed_aggregator.py`
- **功能**: 基于相似度的智能模型聚合

<augment_code_snippet path="FedLLM-SRec-Collaborative/models/federated/fed_aggregator.py" mode="EXCERPT">
````python
def cluster_clients(self, client_parameters: List[Dict[str, torch.Tensor]]) -> np.ndarray:
    """计算客户端模型间的相似度矩阵"""
    # 将参数展平为一维向量
    param_vectors = []
    for params in client_parameters:
        flattened = self.flatten_parameters(params)
        param_vectors.append(flattened.numpy())
    
    # 计算余弦相似度矩阵
    params_matrix = np.vstack(param_vectors)
    similarity_matrix = cosine_similarity(params_matrix)
    normalized_matrix = (similarity_matrix + 1) / 2
    
    return normalized_matrix
````
</augment_code_snippet>

## 📁 项目结构说明

```
FedLLM-SRec-Collaborative/
├── README.md                           # 项目说明
├── requirements.txt                    # 依赖包
├── config/                            # 配置文件
│   └── federated_config.yaml         # 联邦学习配置
├── models/                            # 模型定义
│   └── federated/
│       ├── collaborative_model.py     # 🔥 协同模型主文件
│       └── fed_aggregator.py          # 🔥 联邦聚合器
├── training/
│   └── federated_trainer.py          # 🔥 联邦训练器
├── utils/                             # 工具模块
│   ├── data_utils.py                 # 数据处理
│   └── evaluation.py                 # 评估工具
└── scripts/
    └── run_federated_training.sh     # 🔥 运行脚本
```

## 🚀 快速开始

### 1. 环境配置
```bash
# 进入项目目录
cd FedLLM-SRec-Collaborative

# 安装依赖
pip install -r requirements.txt
```

### 2. 运行训练
```bash
# 使用默认配置运行
bash scripts/run_federated_training.sh

# 自定义参数运行
bash scripts/run_federated_training.sh --clients 3 --rounds 10 --device cuda:0
```

### 3. 查看结果
```bash
# 查看训练日志
tail -f experiments/logs/fedllm_srec_collaborative_*.log

# 查看评估报告
cat experiments/results/*_report.txt
```

## 🔧 核心特性实现

### 1. 代码复用策略
- **FELLRec复用**: 直接复用了`utils.py`中的聚合算法
- **LLM-SRec复用**: 复用了CF-SRec模型架构和知识蒸馏机制
- **创新集成**: 实现了客户端-云端协同架构

### 2. 架构图映射
- ✅ **客户端流程**: 用户设备 → 客户端 → CF-SRec → 用户表示mu
- ✅ **云端流程**: LLM模型 → 优化模块 → 目标函数 → 推荐结果
- ✅ **联邦聚合**: 基于相似度的智能聚合
- ✅ **协同机制**: 知识蒸馏和特征对齐

### 3. 技术创新点
- **大小模型协同**: 客户端轻量级CF-SRec + 云端LLM
- **智能聚合**: 基于模型相似度的个性化聚合
- **隐私保护**: 用户数据不离开客户端
- **端到端优化**: 多目标函数联合优化

## 📊 实验配置建议

### 基础实验
```bash
# 小规模测试
bash scripts/run_federated_training.sh \
    --clients 3 \
    --rounds 5 \
    --device cuda:0

# 完整实验
bash scripts/run_federated_training.sh \
    --clients 5 \
    --rounds 20 \
    --alpha 0.7 \
    --beta 1
```

### 参数调优
- **客户端数量**: 3-10个
- **联邦轮数**: 10-50轮
- **聚合参数**: alpha=0.5-0.9, beta=1-3
- **学习率**: 客户端1e-4, 服务器1e-5

## 🔍 下一步开发计划

### 阶段1: 基础功能验证 ✅
- [x] 项目结构搭建
- [x] 核心模型实现
- [x] 联邦聚合算法
- [x] 训练流程设计

### 阶段2: 功能完善 (待实现)
- [ ] 真实LLM模型集成
- [ ] 数据预处理优化
- [ ] 评估指标完善
- [ ] 可视化工具

### 阶段3: 性能优化 (待实现)
- [ ] 通信效率优化
- [ ] 模型压缩技术
- [ ] 异步训练支持
- [ ] 分布式部署

### 阶段4: 实验验证 (待实现)
- [ ] 多数据集测试
- [ ] 消融实验设计
- [ ] 性能对比分析
- [ ] 论文实验复现

## 🛠️ 技术细节

### 1. 模型架构
- **客户端模型**: 基于SASRec的CF-SRec模型
- **服务器模型**: 基于LLaMA的LLM模型
- **协同机制**: 知识蒸馏 + 特征对齐

### 2. 联邦学习
- **聚合策略**: 基于相似度的智能聚合
- **通信协议**: 参数级通信
- **隐私保护**: 本地训练 + 参数聚合

### 3. 训练策略
- **两阶段训练**: 客户端本地训练 + 服务器知识蒸馏
- **多目标优化**: 推荐损失 + 蒸馏损失 + 对齐损失
- **早停机制**: 基于验证集性能

## 📈 预期效果

### 性能指标
- **NDCG@10**: 预期提升10-15%
- **Hit Rate@10**: 预期提升8-12%
- **通信效率**: 减少60%通信开销
- **隐私保护**: 100%数据本地化

### 技术优势
- 结合联邦学习和大小模型协同
- 保护用户隐私同时提升推荐效果
- 支持异构客户端和动态参与
- 可扩展到大规模部署

## 🤝 贡献指南

1. **Fork项目**: 创建您的功能分支
2. **开发功能**: 遵循代码规范
3. **测试验证**: 确保功能正常
4. **提交PR**: 详细描述修改内容

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 创建Issue讨论技术问题
- 提交PR贡献代码改进
- 参与项目开发和优化

---

**注意**: 这是一个基于您架构图设计的完整实现框架。所有核心组件都已实现，可以直接运行训练。后续可以根据实际需求进行功能扩展和性能优化。
