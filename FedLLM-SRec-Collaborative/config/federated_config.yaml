# FedLLM-SRec协同推荐系统联邦学习配置文件

# ==================== 基础配置 ====================
experiment_name: "fedllm_srec_collaborative"
seed: 42
device: "cuda:0"
log_level: "INFO"

# ==================== 数据配置 ====================
data:
  dataset: "Movies_and_TV"  # 数据集名称
  data_dir: "./data"
  raw_data_dir: "./data/raw"
  processed_data_dir: "./data/processed"
  federated_data_dir: "./data/federated"
  
  # 数据预处理参数
  min_interactions: 5  # 最小交互次数
  max_sequence_length: 128  # 最大序列长度
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

# ==================== 联邦学习配置 ====================
federated:
  client_num: 5  # 客户端数量
  fed_rounds: 20  # 联邦学习轮数
  local_epochs: 3  # 每轮本地训练轮数
  
  # 客户端选择策略
  client_selection: "all"  # all, random, top_k
  participation_rate: 1.0  # 客户端参与率
  
  # 聚合策略
  aggregation_method: "similarity_weighted"  # fedavg, similarity_weighted
  alpha: 0.7  # 聚合权重参数
  beta: 1  # 权重调节参数
  
  # 数据分割策略
  data_split_method: "user_embedding"  # random, user_embedding, iid
  split_alpha: 0.5  # Dirichlet分布参数

# ==================== 客户端模型配置 ====================
client_model:
  model_type: "cf_srec"  # 基于CF-SRec的客户端模型
  hidden_units: 64
  num_blocks: 2
  num_heads: 1
  dropout_rate: 0.2
  
  # LoRA配置
  use_lora: true
  lora_r: 8
  lora_alpha: 16
  lora_dropout: 0.1
  
  # 训练参数
  learning_rate: 1e-4
  batch_size: 32
  weight_decay: 1e-5
  gradient_clip: 1.0

# ==================== 服务器模型配置 ====================
server_model:
  model_type: "llm_srec"  # 基于LLM的服务器模型
  llm_model: "llama-3b"  # llama-3b, llama-7b
  load_in_8bit: true
  
  # LLM配置
  max_length: 512
  temperature: 1.0
  top_p: 0.9
  
  # 知识蒸馏配置
  distillation_temperature: 4.0
  distillation_weight: 0.5
  
  # 训练参数
  learning_rate: 1e-5
  batch_size: 16
  weight_decay: 1e-6
  gradient_accumulation_steps: 2

# ==================== 协同机制配置 ====================
collaboration:
  # 大小模型协同策略
  collaboration_method: "knowledge_distillation"  # knowledge_distillation, feature_alignment
  
  # 通信策略
  communication_rounds: 5  # 每个联邦轮次内的通信轮数
  compression_method: "none"  # none, quantization, sparsification
  
  # 优化目标
  loss_weights:
    recommendation_loss: 1.0
    distillation_loss: 0.5
    alignment_loss: 0.3
    regularization_loss: 0.1

# ==================== 训练配置 ====================
training:
  max_epochs: 50
  patience: 5  # 早停耐心值
  save_every: 5  # 每N轮保存一次模型
  eval_every: 1  # 每N轮评估一次
  
  # 学习率调度
  lr_scheduler: "cosine"  # cosine, linear, exponential
  warmup_steps: 100
  
  # 混合精度训练
  use_fp16: true
  gradient_checkpointing: false

# ==================== 评估配置 ====================
evaluation:
  metrics: ["ndcg", "hit_rate", "recall", "precision"]
  top_k: [5, 10, 20]
  
  # 评估策略
  eval_batch_size: 64
  num_eval_samples: 1000  # 评估样本数量，-1表示全部
  
  # 隐私评估
  privacy_metrics: ["membership_inference", "attribute_inference"]

# ==================== 日志和保存配置 ====================
logging:
  log_dir: "./experiments/logs"
  model_dir: "./experiments/models"
  result_dir: "./experiments/results"
  
  # Wandb配置
  use_wandb: false
  wandb_project: "fedllm-srec-collaborative"
  wandb_entity: "your_entity"
  
  # TensorBoard配置
  use_tensorboard: true
  tensorboard_dir: "./experiments/tensorboard"

# ==================== 硬件配置 ====================
hardware:
  num_workers: 4  # 数据加载器工作进程数
  pin_memory: true
  persistent_workers: true
  
  # 分布式训练
  distributed: false
  world_size: 1
  rank: 0

# ==================== 调试配置 ====================
debug:
  debug_mode: false
  sample_data: false  # 是否使用采样数据进行调试
  sample_ratio: 0.1  # 采样比例
  verbose: true
