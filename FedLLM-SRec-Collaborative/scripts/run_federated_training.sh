#!/bin/bash

# FedLLM-SRec协同推荐系统联邦训练脚本
# 基于架构图实现的大小模型协同联邦推荐系统训练

set -e  # 遇到错误立即退出

# ==================== 配置参数 ====================
# 基础配置
EXPERIMENT_NAME="fedllm_srec_collaborative_$(date +%Y%m%d_%H%M%S)"
DEVICE="cuda:0"
SEED=42

# 数据配置
DATASET="Movies_and_TV"
DATA_DIR="./data"
MAX_SEQ_LENGTH=128

# 联邦学习配置
CLIENT_NUM=5
FED_ROUNDS=20
LOCAL_EPOCHS=3
ALPHA=0.7
BETA=1

# 模型配置
CLIENT_HIDDEN_UNITS=64
CLIENT_NUM_BLOCKS=2
CLIENT_LR=1e-4
CLIENT_BATCH_SIZE=32

SERVER_LLM_MODEL="llama-3b"
SERVER_LR=1e-5
SERVER_BATCH_SIZE=16
DISTILLATION_TEMP=4.0

# 训练配置
MAX_EPOCHS=50
PATIENCE=5
SAVE_EVERY=5

# 日志配置
LOG_DIR="./experiments/logs"
MODEL_DIR="./experiments/models"
RESULT_DIR="./experiments/results"

# ==================== 函数定义 ====================

print_header() {
    echo "=================================================="
    echo "$1"
    echo "=================================================="
}

print_info() {
    echo "[INFO] $1"
}

print_error() {
    echo "[ERROR] $1" >&2
}

check_requirements() {
    print_header "检查环境依赖"
    
    # 检查Python环境
    if ! command -v python &> /dev/null; then
        print_error "Python not found. Please install Python 3.8+"
        exit 1
    fi
    
    # 检查CUDA
    if [[ $DEVICE == cuda* ]]; then
        if ! command -v nvidia-smi &> /dev/null; then
            print_error "NVIDIA GPU not found. Please check CUDA installation."
            exit 1
        fi
        print_info "CUDA available: $(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)"
    fi
    
    # 检查必要的Python包
    python -c "import torch, transformers, peft, sklearn, numpy, pandas" 2>/dev/null || {
        print_error "Required Python packages not found. Please run: pip install -r requirements.txt"
        exit 1
    }
    
    print_info "Environment check passed"
}

prepare_directories() {
    print_header "准备目录结构"
    
    # 创建必要的目录
    mkdir -p "$LOG_DIR"
    mkdir -p "$MODEL_DIR"
    mkdir -p "$RESULT_DIR"
    mkdir -p "$DATA_DIR/raw"
    mkdir -p "$DATA_DIR/processed"
    mkdir -p "$DATA_DIR/federated"
    
    print_info "Directories created successfully"
}

prepare_data() {
    print_header "准备数据集"
    
    # 检查数据是否存在
    if [ ! -f "$DATA_DIR/raw/$DATASET/interactions.json" ]; then
        print_info "Dataset not found. Generating sample data..."
        python -c "
import sys
sys.path.append('.')
from utils.data_utils import prepare_sample_data
prepare_sample_data('$DATA_DIR', '$DATASET')
"
        print_info "Sample data generated"
    else
        print_info "Dataset found: $DATA_DIR/raw/$DATASET"
    fi
}

create_config() {
    print_header "生成配置文件"
    
    CONFIG_FILE="./config/runtime_config.yaml"
    
    cat > "$CONFIG_FILE" << EOF
# FedLLM-SRec运行时配置文件
# 自动生成于: $(date)

experiment_name: "$EXPERIMENT_NAME"
seed: $SEED
device: "$DEVICE"
log_level: "INFO"

# 数据配置
data:
  dataset: "$DATASET"
  data_dir: "$DATA_DIR"
  raw_data_dir: "$DATA_DIR/raw"
  processed_data_dir: "$DATA_DIR/processed"
  federated_data_dir: "$DATA_DIR/federated"
  min_interactions: 5
  max_sequence_length: $MAX_SEQ_LENGTH
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

# 联邦学习配置
federated:
  client_num: $CLIENT_NUM
  fed_rounds: $FED_ROUNDS
  local_epochs: $LOCAL_EPOCHS
  client_selection: "all"
  participation_rate: 1.0
  aggregation_method: "similarity_weighted"
  alpha: $ALPHA
  beta: $BETA
  data_split_method: "user_embedding"
  split_alpha: 0.5

# 客户端模型配置
client_model:
  model_type: "cf_srec"
  item_num: 10000
  hidden_units: $CLIENT_HIDDEN_UNITS
  num_blocks: $CLIENT_NUM_BLOCKS
  num_heads: 1
  dropout_rate: 0.2
  max_sequence_length: $MAX_SEQ_LENGTH
  use_lora: true
  lora_r: 8
  lora_alpha: 16
  lora_dropout: 0.1
  learning_rate: $CLIENT_LR
  batch_size: $CLIENT_BATCH_SIZE
  weight_decay: 1e-5
  gradient_clip: 1.0

# 服务器模型配置
server_model:
  model_type: "llm_srec"
  llm_model: "$SERVER_LLM_MODEL"
  load_in_8bit: true
  max_length: 512
  temperature: 1.0
  top_p: 0.9
  distillation_temperature: $DISTILLATION_TEMP
  distillation_weight: 0.5
  learning_rate: $SERVER_LR
  batch_size: $SERVER_BATCH_SIZE
  weight_decay: 1e-6
  gradient_accumulation_steps: 2

# 协同机制配置
collaboration:
  collaboration_method: "knowledge_distillation"
  communication_rounds: 5
  compression_method: "none"
  loss_weights:
    recommendation_loss: 1.0
    distillation_loss: 0.5
    alignment_loss: 0.3
    regularization_loss: 0.1

# 训练配置
training:
  max_epochs: $MAX_EPOCHS
  patience: $PATIENCE
  save_every: $SAVE_EVERY
  eval_every: 1
  lr_scheduler: "cosine"
  warmup_steps: 100
  use_fp16: true
  gradient_checkpointing: false

# 评估配置
evaluation:
  metrics: ["ndcg", "hit_rate", "recall", "precision"]
  top_k: [5, 10, 20]
  eval_batch_size: 64
  num_eval_samples: 1000
  privacy_metrics: ["membership_inference", "attribute_inference"]

# 日志和保存配置
logging:
  log_dir: "$LOG_DIR"
  model_dir: "$MODEL_DIR"
  result_dir: "$RESULT_DIR"
  use_wandb: false
  wandb_project: "fedllm-srec-collaborative"
  wandb_entity: "your_entity"
  use_tensorboard: true
  tensorboard_dir: "$LOG_DIR/tensorboard"

# 硬件配置
hardware:
  num_workers: 4
  pin_memory: true
  persistent_workers: true
  distributed: false
  world_size: 1
  rank: 0

# 调试配置
debug:
  debug_mode: false
  sample_data: false
  sample_ratio: 0.1
  verbose: true
EOF

    print_info "Configuration file created: $CONFIG_FILE"
}

run_training() {
    print_header "开始联邦训练"
    
    CONFIG_FILE="./config/runtime_config.yaml"
    LOG_FILE="$LOG_DIR/${EXPERIMENT_NAME}.log"
    
    print_info "Experiment: $EXPERIMENT_NAME"
    print_info "Device: $DEVICE"
    print_info "Clients: $CLIENT_NUM"
    print_info "Fed Rounds: $FED_ROUNDS"
    print_info "Log file: $LOG_FILE"
    
    # 运行训练
    python training/federated_trainer.py \
        --config "$CONFIG_FILE" \
        --device "$DEVICE" \
        2>&1 | tee "$LOG_FILE"
    
    if [ $? -eq 0 ]; then
        print_info "Training completed successfully!"
    else
        print_error "Training failed. Check log file: $LOG_FILE"
        exit 1
    fi
}

evaluate_results() {
    print_header "评估结果"
    
    BEST_MODEL_DIR="$MODEL_DIR/best"
    
    if [ -d "$BEST_MODEL_DIR" ]; then
        print_info "Best model found at: $BEST_MODEL_DIR"
        
        # 运行评估脚本
        python -c "
import sys
sys.path.append('.')
import yaml
import torch
from utils.evaluation import RecommendationEvaluator

# 加载配置
with open('./config/runtime_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# 加载指标
metrics_file = '$BEST_MODEL_DIR/metrics.yaml'
try:
    with open(metrics_file, 'r') as f:
        metrics = yaml.safe_load(f)
    
    evaluator = RecommendationEvaluator(config)
    report = evaluator.generate_evaluation_report(metrics)
    print(report)
    
    # 保存报告
    with open('$RESULT_DIR/${EXPERIMENT_NAME}_report.txt', 'w') as f:
        f.write(report)
    
    print(f'\\nDetailed report saved to: $RESULT_DIR/${EXPERIMENT_NAME}_report.txt')
    
except Exception as e:
    print(f'Error generating report: {e}')
"
    else
        print_error "Best model not found. Training may have failed."
    fi
}

cleanup() {
    print_header "清理临时文件"
    
    # 清理临时文件（如果需要）
    # rm -f ./config/runtime_config.yaml
    
    print_info "Cleanup completed"
}

show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --clients NUM        Number of clients (default: 5)"
    echo "  --rounds NUM         Number of federated rounds (default: 20)"
    echo "  --device DEVICE      Device to use (default: cuda:0)"
    echo "  --dataset NAME       Dataset name (default: Movies_and_TV)"
    echo "  --alpha FLOAT        Aggregation alpha parameter (default: 0.7)"
    echo "  --beta INT           Aggregation beta parameter (default: 1)"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Run with default settings"
    echo "  $0 --clients 3 --rounds 10           # Run with 3 clients for 10 rounds"
    echo "  $0 --device cpu --dataset Books      # Run on CPU with Books dataset"
}

# ==================== 参数解析 ====================

while [[ $# -gt 0 ]]; do
    case $1 in
        --clients)
            CLIENT_NUM="$2"
            shift 2
            ;;
        --rounds)
            FED_ROUNDS="$2"
            shift 2
            ;;
        --device)
            DEVICE="$2"
            shift 2
            ;;
        --dataset)
            DATASET="$2"
            shift 2
            ;;
        --alpha)
            ALPHA="$2"
            shift 2
            ;;
        --beta)
            BETA="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# ==================== 主执行流程 ====================

main() {
    print_header "FedLLM-SRec协同推荐系统联邦训练"
    
    # 执行训练流程
    check_requirements
    prepare_directories
    prepare_data
    create_config
    run_training
    evaluate_results
    
    print_header "训练完成"
    print_info "实验名称: $EXPERIMENT_NAME"
    print_info "模型保存: $MODEL_DIR"
    print_info "日志文件: $LOG_DIR"
    print_info "结果报告: $RESULT_DIR"
    
    echo ""
    echo "🎉 FedLLM-SRec联邦训练成功完成！"
    echo ""
    echo "📊 查看结果:"
    echo "   - 模型文件: $MODEL_DIR/best/"
    echo "   - 训练日志: $LOG_DIR/${EXPERIMENT_NAME}.log"
    echo "   - 评估报告: $RESULT_DIR/${EXPERIMENT_NAME}_report.txt"
    echo ""
    echo "🔧 下一步:"
    echo "   - 调整超参数重新训练"
    echo "   - 使用不同数据集测试"
    echo "   - 部署模型进行推理"
}

# 捕获中断信号
trap cleanup EXIT

# 运行主函数
main "$@"
