"""
联邦训练器 - 大小模型协同联邦推荐系统训练主程序

实现基于架构图的完整训练流程：
1. 客户端本地训练（CF-SRec模型）
2. 服务器知识蒸馏（LLM模型）
3. 联邦聚合（基于相似度）
4. 协同优化（大小模型协同）
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
import yaml
from typing import Dict, List, Tuple, Optional, Any
from tqdm import tqdm
import wandb
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.federated.collaborative_model import CollaborativeRecommendationModel
from models.federated.fed_aggregator import FederatedAggregator
from utils.data_utils import FederatedDataLoader
from utils.evaluation import RecommendationEvaluator
from utils.fed_utils import setup_logging, save_checkpoint, load_checkpoint

logger = logging.getLogger(__name__)


class FederatedTrainer:
    """
    联邦训练器
    
    实现大小模型协同联邦推荐系统的完整训练流程：
    1. 多客户端本地训练
    2. 服务器端知识蒸馏
    3. 智能联邦聚合
    4. 协同优化机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = config.get('device', 'cuda:0')
        self.client_num = config['federated']['client_num']
        self.fed_rounds = config['federated']['fed_rounds']
        self.local_epochs = config['federated']['local_epochs']
        
        # 初始化组件
        self._init_logging()
        self._init_models()
        self._init_data()
        self._init_optimizers()
        self._init_evaluator()
        self._init_aggregator()
        
        # 训练状态
        self.current_round = 0
        self.best_performance = 0.0
        self.patience_counter = 0
        
        logger.info("FederatedTrainer initialized successfully")
    
    def _init_logging(self):
        """初始化日志系统"""
        setup_logging(self.config['logging'])
        
        # 初始化Wandb
        if self.config['logging'].get('use_wandb', False):
            wandb.init(
                project=self.config['logging']['wandb_project'],
                entity=self.config['logging'].get('wandb_entity'),
                config=self.config,
                name=f"fedllm_srec_{self.config['experiment_name']}"
            )
        
        # 初始化TensorBoard
        if self.config['logging'].get('use_tensorboard', True):
            self.tb_writer = SummaryWriter(
                log_dir=os.path.join(
                    self.config['logging']['tensorboard_dir'],
                    self.config['experiment_name']
                )
            )
    
    def _init_models(self):
        """初始化模型"""
        # 创建全局模型
        self.global_model = CollaborativeRecommendationModel(self.config).to(self.device)
        
        # 创建客户端模型列表
        self.client_models = []
        for i in range(self.client_num):
            client_model = CollaborativeRecommendationModel(self.config).to(self.device)
            # 复制全局模型参数
            client_model.load_state_dict(self.global_model.state_dict())
            self.client_models.append(client_model)
        
        logger.info(f"Initialized {self.client_num} client models and 1 global model")
    
    def _init_data(self):
        """初始化数据加载器"""
        self.data_loader = FederatedDataLoader(self.config)
        
        # 获取联邦数据分割
        self.client_train_loaders, self.client_val_loaders, self.test_loader = \
            self.data_loader.get_federated_dataloaders()
        
        logger.info(f"Initialized federated data loaders for {self.client_num} clients")
    
    def _init_optimizers(self):
        """初始化优化器"""
        client_config = self.config['client_model']
        server_config = self.config['server_model']
        
        # 客户端优化器
        self.client_optimizers = []
        for client_model in self.client_models:
            optimizer = torch.optim.AdamW(
                client_model.parameters(),
                lr=client_config['learning_rate'],
                weight_decay=client_config.get('weight_decay', 1e-5)
            )
            self.client_optimizers.append(optimizer)
        
        # 服务器优化器
        self.server_optimizer = torch.optim.AdamW(
            self.global_model.parameters(),
            lr=server_config['learning_rate'],
            weight_decay=server_config.get('weight_decay', 1e-6)
        )
        
        logger.info("Initialized optimizers for clients and server")
    
    def _init_evaluator(self):
        """初始化评估器"""
        self.evaluator = RecommendationEvaluator(self.config)
        logger.info("Initialized recommendation evaluator")
    
    def _init_aggregator(self):
        """初始化联邦聚合器"""
        self.aggregator = FederatedAggregator(self.config)
        logger.info("Initialized federated aggregator")
    
    def client_local_training(self, client_id: int, num_epochs: int) -> Dict[str, float]:
        """
        客户端本地训练
        
        Args:
            client_id: 客户端ID
            num_epochs: 本地训练轮数
            
        Returns:
            metrics: 训练指标
        """
        client_model = self.client_models[client_id]
        optimizer = self.client_optimizers[client_id]
        train_loader = self.client_train_loaders[client_id]
        
        client_model.train()
        total_loss = 0.0
        num_batches = 0
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            
            for batch_idx, batch in enumerate(train_loader):
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                optimizer.zero_grad()
                
                # 客户端前向传播
                outputs = client_model(batch, mode='client_only')
                
                # 计算损失
                targets = {
                    'target_items': batch['target_items'],
                    'client_features': outputs['user_representations']
                }
                losses = client_model.compute_loss(outputs, targets)
                
                # 反向传播
                losses['total_loss'].backward()
                
                # 梯度裁剪
                if self.config['client_model'].get('gradient_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        client_model.parameters(),
                        self.config['client_model']['gradient_clip']
                    )
                
                optimizer.step()
                
                epoch_loss += losses['total_loss'].item()
                num_batches += 1
            
            total_loss += epoch_loss
            
            logger.debug(f"Client {client_id} Epoch {epoch+1}/{num_epochs}, Loss: {epoch_loss:.4f}")
        
        avg_loss = total_loss / (num_epochs * len(train_loader))
        
        # 评估客户端模型
        val_metrics = self.evaluate_client(client_id)
        
        metrics = {
            'train_loss': avg_loss,
            'val_ndcg': val_metrics.get('ndcg@10', 0.0),
            'val_hit_rate': val_metrics.get('hit_rate@10', 0.0)
        }
        
        logger.info(f"Client {client_id} training completed. Loss: {avg_loss:.4f}, "
                   f"Val NDCG@10: {metrics['val_ndcg']:.4f}")
        
        return metrics
    
    def server_generate_teacher_knowledge(self, client_representations: List[torch.Tensor]) -> Dict[int, torch.Tensor]:
        """
        服务器端生成教师知识（基于LLM-SRec方法）

        Args:
            client_representations: 客户端用户表示列表

        Returns:
            teacher_knowledge: 每个客户端对应的教师知识字典
        """
        self.global_model.eval()
        teacher_knowledge = {}

        logger.info("Generating teacher knowledge from cloud LLM")

        with torch.no_grad():
            for client_id, user_representations in enumerate(client_representations):
                if user_representations.shape[0] > 0:  # 检查是否有数据
                    # 云端LLM生成教师分布
                    teacher_distributions = self.global_model.server_generate_teacher_knowledge(
                        user_representations
                    )
                    teacher_knowledge[client_id] = teacher_distributions

                    logger.debug(f"Generated teacher knowledge for client {client_id}: "
                               f"shape {teacher_distributions.shape}")
                else:
                    # 如果客户端没有数据，创建空的教师知识
                    teacher_knowledge[client_id] = torch.empty(0, self.global_model.item_num).to(self.device)

        logger.info(f"Teacher knowledge generation completed for {len(teacher_knowledge)} clients")

        return teacher_knowledge

    def client_distillation_training(self, client_id: int, teacher_distributions: torch.Tensor,
                                   num_epochs: int = 1) -> Dict[str, float]:
        """
        客户端蒸馏训练（基于LLM-SRec方法）

        Args:
            client_id: 客户端ID
            teacher_distributions: 教师分布
            num_epochs: 蒸馏训练轮数

        Returns:
            metrics: 蒸馏训练指标
        """
        client_model = self.client_models[client_id]
        optimizer = self.client_optimizers[client_id]
        train_loader = self.client_train_loaders[client_id]

        if len(train_loader) == 0:
            logger.warning(f"Client {client_id} has no training data, skipping distillation")
            return {'distillation_loss': 0.0, 'num_batches': 0}

        client_model.train()
        total_loss = 0.0
        total_rec_loss = 0.0
        total_kd_loss = 0.0
        num_batches = 0

        # 蒸馏权重
        alpha = self.config['collaboration']['loss_weights'].get('recommendation_loss', 0.7)
        beta = self.config['collaboration']['loss_weights'].get('distillation_loss', 0.3)

        for epoch in range(num_epochs):
            for batch_idx, batch in enumerate(train_loader):
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                optimizer.zero_grad()

                # 1. 计算推荐损失
                outputs = client_model(batch, mode='client_only')
                targets = {'target_items': batch['target_items']}
                rec_losses = client_model.compute_loss(outputs, targets)
                rec_loss = rec_losses.get('recommendation_loss', rec_losses['total_loss'])

                # 2. 计算知识蒸馏损失
                # 为当前批次选择对应的教师分布
                batch_size = batch['user_sequences'].shape[0]
                if teacher_distributions.shape[0] >= batch_size:
                    batch_teacher_dist = teacher_distributions[:batch_size]
                else:
                    # 如果教师分布不够，重复使用
                    repeat_times = (batch_size + teacher_distributions.shape[0] - 1) // teacher_distributions.shape[0]
                    expanded_teacher = teacher_distributions.repeat(repeat_times, 1)
                    batch_teacher_dist = expanded_teacher[:batch_size]

                kd_loss = client_model.compute_distillation_loss_only(
                    batch['user_sequences'], batch_teacher_dist
                )

                # 3. 联合损失（LLM-SRec风格）
                total_batch_loss = alpha * rec_loss + beta * kd_loss

                # 反向传播
                total_batch_loss.backward()

                # 梯度裁剪
                if self.config['client_model'].get('gradient_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        client_model.parameters(),
                        self.config['client_model']['gradient_clip']
                    )

                optimizer.step()

                # 记录损失
                total_loss += total_batch_loss.item()
                total_rec_loss += rec_loss.item()
                total_kd_loss += kd_loss.item()
                num_batches += 1

        # 计算平均损失
        avg_total_loss = total_loss / num_batches if num_batches > 0 else 0.0
        avg_rec_loss = total_rec_loss / num_batches if num_batches > 0 else 0.0
        avg_kd_loss = total_kd_loss / num_batches if num_batches > 0 else 0.0

        metrics = {
            'total_loss': avg_total_loss,
            'recommendation_loss': avg_rec_loss,
            'distillation_loss': avg_kd_loss,
            'num_batches': num_batches
        }

        logger.info(f"Client {client_id} distillation training completed. "
                   f"Total Loss: {avg_total_loss:.4f}, Rec Loss: {avg_rec_loss:.4f}, "
                   f"KD Loss: {avg_kd_loss:.4f}")

        return metrics
    
    def federated_aggregation(self) -> Dict[str, Any]:
        """
        联邦聚合
        
        Returns:
            aggregation_stats: 聚合统计信息
        """
        # 提取客户端参数
        client_parameters = []
        for client_model in self.client_models:
            params = self.aggregator.extract_model_parameters(client_model)
            client_parameters.append(params)
        
        # 执行聚合
        if self.config['federated']['aggregation_method'] == 'similarity_weighted':
            # 基于相似度的个性化聚合
            aggregated_params_list = self.aggregator.aggregate(client_parameters)
            
            # 更新每个客户端模型
            for i, (client_model, aggregated_params) in enumerate(zip(self.client_models, aggregated_params_list)):
                self.aggregator.update_model_parameters(client_model, aggregated_params)
        else:
            # 标准聚合
            aggregated_params = self.aggregator.aggregate(client_parameters)
            
            # 更新全局模型
            self.aggregator.update_model_parameters(self.global_model, aggregated_params)
            
            # 将全局模型参数分发给客户端
            for client_model in self.client_models:
                client_model.load_state_dict(self.global_model.state_dict())
        
        # 获取聚合统计信息
        aggregation_stats = self.aggregator.get_aggregation_statistics(client_parameters)
        
        logger.info(f"Federated aggregation completed. Diversity: {aggregation_stats['diversity']:.4f}")
        
        return aggregation_stats
    
    def evaluate_client(self, client_id: int) -> Dict[str, float]:
        """评估单个客户端"""
        client_model = self.client_models[client_id]
        val_loader = self.client_val_loaders[client_id]
        
        return self.evaluator.evaluate_model(client_model, val_loader)
    
    def evaluate_global_model(self) -> Dict[str, float]:
        """评估全局模型"""
        return self.evaluator.evaluate_model(self.global_model, self.test_loader)
    
    def train_one_round(self) -> Dict[str, Any]:
        """
        训练一轮联邦学习（包含LLM-SRec风格的知识蒸馏）

        Returns:
            round_metrics: 本轮训练指标
        """
        logger.info(f"Starting federated round {self.current_round + 1}/{self.fed_rounds}")

        round_metrics = {
            'round': self.current_round + 1,
            'client_metrics': [],
            'teacher_generation_metrics': {},
            'distillation_metrics': [],
            'aggregation_stats': {},
            'global_metrics': {}
        }

        # 1. 客户端本地训练（生成用户表示）
        client_representations = []
        for client_id in range(self.client_num):
            logger.info(f"Local training for client {client_id + 1}/{self.client_num}")

            client_metrics = self.client_local_training(client_id, self.local_epochs)
            round_metrics['client_metrics'].append(client_metrics)

            # 收集客户端用户表示用于教师知识生成
            client_model = self.client_models[client_id]
            val_loader = self.client_val_loaders[client_id]

            client_model.eval()
            with torch.no_grad():
                batch_representations = []
                for batch in val_loader:
                    batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                            for k, v in batch.items()}
                    outputs = client_model(batch, mode='client_only')
                    batch_representations.append(outputs['user_representations'])

                if batch_representations:
                    client_repr = torch.cat(batch_representations, dim=0)
                    client_representations.append(client_repr)
                else:
                    # 如果没有数据，添加空张量
                    client_representations.append(torch.empty(0, self.config['client_model']['hidden_units']).to(self.device))

        # 2. 云端LLM生成教师知识
        logger.info("Generating teacher knowledge from cloud LLM")
        teacher_knowledge = self.server_generate_teacher_knowledge(client_representations)

        teacher_metrics = {
            'num_clients_with_knowledge': len([k for k, v in teacher_knowledge.items() if v.shape[0] > 0]),
            'total_teacher_samples': sum(v.shape[0] for v in teacher_knowledge.values())
        }
        round_metrics['teacher_generation_metrics'] = teacher_metrics

        # 3. 客户端蒸馏学习
        logger.info("Performing client distillation learning")
        distillation_metrics = []
        for client_id in range(self.client_num):
            if client_id in teacher_knowledge and teacher_knowledge[client_id].shape[0] > 0:
                logger.info(f"Distillation training for client {client_id + 1}/{self.client_num}")

                distill_metrics = self.client_distillation_training(
                    client_id,
                    teacher_knowledge[client_id],
                    num_epochs=1  # 蒸馏训练轮数
                )
                distillation_metrics.append(distill_metrics)
            else:
                logger.warning(f"Client {client_id} has no teacher knowledge, skipping distillation")
                distillation_metrics.append({'distillation_loss': 0.0, 'num_batches': 0})

        round_metrics['distillation_metrics'] = distillation_metrics

        # 4. 联邦聚合
        logger.info("Performing federated aggregation")
        aggregation_stats = self.federated_aggregation()
        round_metrics['aggregation_stats'] = aggregation_stats

        # 5. 全局评估
        logger.info("Evaluating global model")
        global_metrics = self.evaluate_global_model()
        round_metrics['global_metrics'] = global_metrics

        # 6. 记录指标
        self._log_metrics(round_metrics)

        # 7. 保存检查点
        if (self.current_round + 1) % self.config['training'].get('save_every', 5) == 0:
            self._save_checkpoint(round_metrics)

        self.current_round += 1

        # 计算平均蒸馏损失
        avg_distill_loss = np.mean([m['distillation_loss'] for m in distillation_metrics if m['num_batches'] > 0])

        logger.info(f"Round {self.current_round} completed. "
                   f"Global NDCG@10: {global_metrics.get('ndcg@10', 0.0):.4f}, "
                   f"Avg Distillation Loss: {avg_distill_loss:.4f}")

        return round_metrics
    
    def train(self):
        """主训练循环"""
        logger.info("Starting federated training")
        
        training_history = []
        
        for round_idx in range(self.fed_rounds):
            try:
                round_metrics = self.train_one_round()
                training_history.append(round_metrics)
                
                # 早停检查
                current_performance = round_metrics['global_metrics'].get('ndcg@10', 0.0)
                if current_performance > self.best_performance:
                    self.best_performance = current_performance
                    self.patience_counter = 0
                    
                    # 保存最佳模型
                    self._save_best_model(round_metrics)
                else:
                    self.patience_counter += 1
                
                # 早停
                patience = self.config['training'].get('patience', 5)
                if self.patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {patience} rounds without improvement")
                    break
                    
            except Exception as e:
                logger.error(f"Error in round {round_idx + 1}: {str(e)}")
                raise e
        
        logger.info("Federated training completed")
        
        # 最终评估
        final_metrics = self.evaluate_global_model()
        logger.info(f"Final performance - NDCG@10: {final_metrics.get('ndcg@10', 0.0):.4f}, "
                   f"Hit Rate@10: {final_metrics.get('hit_rate@10', 0.0):.4f}")
        
        return training_history
    
    def _log_metrics(self, metrics: Dict[str, Any]):
        """记录训练指标（包含知识蒸馏指标）"""
        round_num = metrics['round']

        # 计算平均客户端指标
        if metrics['client_metrics']:
            avg_client_loss = np.mean([m['train_loss'] for m in metrics['client_metrics']])
            avg_client_ndcg = np.mean([m['val_ndcg'] for m in metrics['client_metrics']])

            # 计算平均蒸馏指标
            distill_metrics = metrics.get('distillation_metrics', [])
            if distill_metrics:
                valid_distill_metrics = [m for m in distill_metrics if m['num_batches'] > 0]
                if valid_distill_metrics:
                    avg_distill_loss = np.mean([m['distillation_loss'] for m in valid_distill_metrics])
                    avg_rec_loss = np.mean([m.get('recommendation_loss', 0) for m in valid_distill_metrics])
                else:
                    avg_distill_loss = 0.0
                    avg_rec_loss = 0.0
            else:
                avg_distill_loss = 0.0
                avg_rec_loss = 0.0

            # TensorBoard记录
            if hasattr(self, 'tb_writer'):
                self.tb_writer.add_scalar('Client/AvgTrainLoss', avg_client_loss, round_num)
                self.tb_writer.add_scalar('Client/AvgValNDCG', avg_client_ndcg, round_num)
                self.tb_writer.add_scalar('Distillation/AvgDistillationLoss', avg_distill_loss, round_num)
                self.tb_writer.add_scalar('Distillation/AvgRecommendationLoss', avg_rec_loss, round_num)

                # 教师知识生成指标
                teacher_metrics = metrics.get('teacher_generation_metrics', {})
                if teacher_metrics:
                    self.tb_writer.add_scalar('Teacher/ClientsWithKnowledge',
                                            teacher_metrics.get('num_clients_with_knowledge', 0), round_num)
                    self.tb_writer.add_scalar('Teacher/TotalSamples',
                                            teacher_metrics.get('total_teacher_samples', 0), round_num)

            # Wandb记录
            if self.config['logging'].get('use_wandb', False):
                log_data = {
                    'round': round_num,
                    'client_avg_train_loss': avg_client_loss,
                    'client_avg_val_ndcg': avg_client_ndcg,
                    'avg_distillation_loss': avg_distill_loss,
                    'avg_recommendation_loss': avg_rec_loss,
                    **metrics['global_metrics'],
                    **metrics['aggregation_stats']
                }

                # 添加教师知识指标
                teacher_metrics = metrics.get('teacher_generation_metrics', {})
                if teacher_metrics:
                    log_data.update({
                        'teacher_clients_with_knowledge': teacher_metrics.get('num_clients_with_knowledge', 0),
                        'teacher_total_samples': teacher_metrics.get('total_teacher_samples', 0)
                    })

                wandb.log(log_data)

            # 控制台日志
            logger.info(f"Round {round_num} Metrics Summary:")
            logger.info(f"  Client Avg Train Loss: {avg_client_loss:.4f}")
            logger.info(f"  Client Avg Val NDCG: {avg_client_ndcg:.4f}")
            logger.info(f"  Avg Distillation Loss: {avg_distill_loss:.4f}")
            logger.info(f"  Avg Recommendation Loss: {avg_rec_loss:.4f}")

            if 'global_metrics' in metrics:
                global_ndcg = metrics['global_metrics'].get('ndcg@10', 0.0)
                global_hr = metrics['global_metrics'].get('hit_rate@10', 0.0)
                logger.info(f"  Global NDCG@10: {global_ndcg:.4f}")
                logger.info(f"  Global Hit Rate@10: {global_hr:.4f}")
    
    def _save_checkpoint(self, metrics: Dict[str, Any]):
        """保存检查点"""
        checkpoint_dir = os.path.join(self.config['logging']['model_dir'], 'checkpoints')
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(checkpoint_dir, f'round_{self.current_round}.pt')
        
        checkpoint = {
            'round': self.current_round,
            'global_model_state': self.global_model.state_dict(),
            'client_model_states': [model.state_dict() for model in self.client_models],
            'server_optimizer_state': self.server_optimizer.state_dict(),
            'client_optimizer_states': [opt.state_dict() for opt in self.client_optimizers],
            'metrics': metrics,
            'config': self.config
        }
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved to {checkpoint_path}")
    
    def _save_best_model(self, metrics: Dict[str, Any]):
        """保存最佳模型"""
        best_model_dir = os.path.join(self.config['logging']['model_dir'], 'best')
        os.makedirs(best_model_dir, exist_ok=True)
        
        # 保存全局模型
        global_model_path = os.path.join(best_model_dir, 'global_model.pt')
        torch.save(self.global_model.state_dict(), global_model_path)
        
        # 保存客户端模型
        for i, client_model in enumerate(self.client_models):
            client_model_path = os.path.join(best_model_dir, f'client_{i}_model.pt')
            torch.save(client_model.state_dict(), client_model_path)
        
        # 保存指标
        metrics_path = os.path.join(best_model_dir, 'metrics.yaml')
        with open(metrics_path, 'w') as f:
            yaml.dump(metrics, f)
        
        logger.info(f"Best model saved to {best_model_dir}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='FedLLM-SRec Collaborative Training')
    parser.add_argument('--config', type=str, default='config/federated_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='Device to use for training')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # 覆盖设备配置
    config['device'] = args.device
    
    # 创建训练器并开始训练
    trainer = FederatedTrainer(config)
    training_history = trainer.train()
    
    print("Training completed successfully!")


if __name__ == '__main__':
    main()
