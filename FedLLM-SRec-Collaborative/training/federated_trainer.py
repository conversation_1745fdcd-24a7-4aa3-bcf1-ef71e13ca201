"""
联邦训练器 - 大小模型协同联邦推荐系统训练主程序

实现基于架构图的完整训练流程：
1. 客户端本地训练（CF-SRec模型）
2. 服务器知识蒸馏（LLM模型）
3. 联邦聚合（基于相似度）
4. 协同优化（大小模型协同）
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import logging
import yaml
from typing import Dict, List, Tuple, Optional, Any
from tqdm import tqdm
import wandb
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.federated.collaborative_model import CollaborativeRecommendationModel
from models.federated.fed_aggregator import FederatedAggregator
from utils.data_utils import FederatedDataLoader
from utils.evaluation import RecommendationEvaluator
from utils.fed_utils import setup_logging, save_checkpoint, load_checkpoint

logger = logging.getLogger(__name__)


class FederatedTrainer:
    """
    联邦训练器
    
    实现大小模型协同联邦推荐系统的完整训练流程：
    1. 多客户端本地训练
    2. 服务器端知识蒸馏
    3. 智能联邦聚合
    4. 协同优化机制
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = config.get('device', 'cuda:0')
        self.client_num = config['federated']['client_num']
        self.fed_rounds = config['federated']['fed_rounds']
        self.local_epochs = config['federated']['local_epochs']
        
        # 初始化组件
        self._init_logging()
        self._init_models()
        self._init_data()
        self._init_optimizers()
        self._init_evaluator()
        self._init_aggregator()
        
        # 训练状态
        self.current_round = 0
        self.best_performance = 0.0
        self.patience_counter = 0
        
        logger.info("FederatedTrainer initialized successfully")
    
    def _init_logging(self):
        """初始化日志系统"""
        setup_logging(self.config['logging'])
        
        # 初始化Wandb
        if self.config['logging'].get('use_wandb', False):
            wandb.init(
                project=self.config['logging']['wandb_project'],
                entity=self.config['logging'].get('wandb_entity'),
                config=self.config,
                name=f"fedllm_srec_{self.config['experiment_name']}"
            )
        
        # 初始化TensorBoard
        if self.config['logging'].get('use_tensorboard', True):
            self.tb_writer = SummaryWriter(
                log_dir=os.path.join(
                    self.config['logging']['tensorboard_dir'],
                    self.config['experiment_name']
                )
            )
    
    def _init_models(self):
        """初始化模型"""
        # 创建全局模型
        self.global_model = CollaborativeRecommendationModel(self.config).to(self.device)
        
        # 创建客户端模型列表
        self.client_models = []
        for i in range(self.client_num):
            client_model = CollaborativeRecommendationModel(self.config).to(self.device)
            # 复制全局模型参数
            client_model.load_state_dict(self.global_model.state_dict())
            self.client_models.append(client_model)
        
        logger.info(f"Initialized {self.client_num} client models and 1 global model")
    
    def _init_data(self):
        """初始化数据加载器"""
        self.data_loader = FederatedDataLoader(self.config)
        
        # 获取联邦数据分割
        self.client_train_loaders, self.client_val_loaders, self.test_loader = \
            self.data_loader.get_federated_dataloaders()
        
        logger.info(f"Initialized federated data loaders for {self.client_num} clients")
    
    def _init_optimizers(self):
        """初始化优化器"""
        client_config = self.config['client_model']
        server_config = self.config['server_model']
        
        # 客户端优化器
        self.client_optimizers = []
        for client_model in self.client_models:
            optimizer = torch.optim.AdamW(
                client_model.parameters(),
                lr=client_config['learning_rate'],
                weight_decay=client_config.get('weight_decay', 1e-5)
            )
            self.client_optimizers.append(optimizer)
        
        # 服务器优化器
        self.server_optimizer = torch.optim.AdamW(
            self.global_model.parameters(),
            lr=server_config['learning_rate'],
            weight_decay=server_config.get('weight_decay', 1e-6)
        )
        
        logger.info("Initialized optimizers for clients and server")
    
    def _init_evaluator(self):
        """初始化评估器"""
        self.evaluator = RecommendationEvaluator(self.config)
        logger.info("Initialized recommendation evaluator")
    
    def _init_aggregator(self):
        """初始化联邦聚合器"""
        self.aggregator = FederatedAggregator(self.config)
        logger.info("Initialized federated aggregator")
    
    def client_local_training(self, client_id: int, num_epochs: int) -> Dict[str, float]:
        """
        客户端本地训练
        
        Args:
            client_id: 客户端ID
            num_epochs: 本地训练轮数
            
        Returns:
            metrics: 训练指标
        """
        client_model = self.client_models[client_id]
        optimizer = self.client_optimizers[client_id]
        train_loader = self.client_train_loaders[client_id]
        
        client_model.train()
        total_loss = 0.0
        num_batches = 0
        
        for epoch in range(num_epochs):
            epoch_loss = 0.0
            
            for batch_idx, batch in enumerate(train_loader):
                # 移动数据到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                optimizer.zero_grad()
                
                # 客户端前向传播
                outputs = client_model(batch, mode='client_only')
                
                # 计算损失
                targets = {
                    'target_items': batch['target_items'],
                    'client_features': outputs['user_representations']
                }
                losses = client_model.compute_loss(outputs, targets)
                
                # 反向传播
                losses['total_loss'].backward()
                
                # 梯度裁剪
                if self.config['client_model'].get('gradient_clip', 0) > 0:
                    torch.nn.utils.clip_grad_norm_(
                        client_model.parameters(),
                        self.config['client_model']['gradient_clip']
                    )
                
                optimizer.step()
                
                epoch_loss += losses['total_loss'].item()
                num_batches += 1
            
            total_loss += epoch_loss
            
            logger.debug(f"Client {client_id} Epoch {epoch+1}/{num_epochs}, Loss: {epoch_loss:.4f}")
        
        avg_loss = total_loss / (num_epochs * len(train_loader))
        
        # 评估客户端模型
        val_metrics = self.evaluate_client(client_id)
        
        metrics = {
            'train_loss': avg_loss,
            'val_ndcg': val_metrics.get('ndcg@10', 0.0),
            'val_hit_rate': val_metrics.get('hit_rate@10', 0.0)
        }
        
        logger.info(f"Client {client_id} training completed. Loss: {avg_loss:.4f}, "
                   f"Val NDCG@10: {metrics['val_ndcg']:.4f}")
        
        return metrics
    
    def server_knowledge_distillation(self, client_representations: List[torch.Tensor]) -> Dict[str, float]:
        """
        服务器端知识蒸馏
        
        Args:
            client_representations: 客户端用户表示列表
            
        Returns:
            metrics: 蒸馏指标
        """
        self.global_model.train()
        total_loss = 0.0
        num_batches = 0
        
        # 合并客户端表示
        all_representations = torch.cat(client_representations, dim=0)
        
        # 创建服务器训练批次
        batch_size = self.config['server_model']['batch_size']
        num_samples = all_representations.shape[0]
        
        for start_idx in range(0, num_samples, batch_size):
            end_idx = min(start_idx + batch_size, num_samples)
            batch_representations = all_representations[start_idx:end_idx]
            
            self.server_optimizer.zero_grad()
            
            # 服务器前向传播
            batch_data = {'user_representations': batch_representations}
            outputs = self.global_model(batch_data, mode='server_only')
            
            # 知识蒸馏损失
            targets = {
                'client_features': batch_representations,
                'alignment_features': outputs['alignment_features']
            }
            losses = self.global_model.compute_loss(outputs, targets)
            
            # 反向传播
            losses['total_loss'].backward()
            
            # 梯度累积
            if (num_batches + 1) % self.config['server_model'].get('gradient_accumulation_steps', 1) == 0:
                self.server_optimizer.step()
                self.server_optimizer.zero_grad()
            
            total_loss += losses['total_loss'].item()
            num_batches += 1
        
        avg_loss = total_loss / num_batches
        
        metrics = {
            'distillation_loss': avg_loss,
            'num_samples': num_samples
        }
        
        logger.info(f"Server knowledge distillation completed. Loss: {avg_loss:.4f}")
        
        return metrics
    
    def federated_aggregation(self) -> Dict[str, Any]:
        """
        联邦聚合
        
        Returns:
            aggregation_stats: 聚合统计信息
        """
        # 提取客户端参数
        client_parameters = []
        for client_model in self.client_models:
            params = self.aggregator.extract_model_parameters(client_model)
            client_parameters.append(params)
        
        # 执行聚合
        if self.config['federated']['aggregation_method'] == 'similarity_weighted':
            # 基于相似度的个性化聚合
            aggregated_params_list = self.aggregator.aggregate(client_parameters)
            
            # 更新每个客户端模型
            for i, (client_model, aggregated_params) in enumerate(zip(self.client_models, aggregated_params_list)):
                self.aggregator.update_model_parameters(client_model, aggregated_params)
        else:
            # 标准聚合
            aggregated_params = self.aggregator.aggregate(client_parameters)
            
            # 更新全局模型
            self.aggregator.update_model_parameters(self.global_model, aggregated_params)
            
            # 将全局模型参数分发给客户端
            for client_model in self.client_models:
                client_model.load_state_dict(self.global_model.state_dict())
        
        # 获取聚合统计信息
        aggregation_stats = self.aggregator.get_aggregation_statistics(client_parameters)
        
        logger.info(f"Federated aggregation completed. Diversity: {aggregation_stats['diversity']:.4f}")
        
        return aggregation_stats
    
    def evaluate_client(self, client_id: int) -> Dict[str, float]:
        """评估单个客户端"""
        client_model = self.client_models[client_id]
        val_loader = self.client_val_loaders[client_id]
        
        return self.evaluator.evaluate_model(client_model, val_loader)
    
    def evaluate_global_model(self) -> Dict[str, float]:
        """评估全局模型"""
        return self.evaluator.evaluate_model(self.global_model, self.test_loader)
    
    def train_one_round(self) -> Dict[str, Any]:
        """
        训练一轮联邦学习
        
        Returns:
            round_metrics: 本轮训练指标
        """
        logger.info(f"Starting federated round {self.current_round + 1}/{self.fed_rounds}")
        
        round_metrics = {
            'round': self.current_round + 1,
            'client_metrics': [],
            'server_metrics': {},
            'aggregation_stats': {},
            'global_metrics': {}
        }
        
        # 1. 客户端本地训练
        client_representations = []
        for client_id in range(self.client_num):
            logger.info(f"Training client {client_id + 1}/{self.client_num}")
            
            client_metrics = self.client_local_training(client_id, self.local_epochs)
            round_metrics['client_metrics'].append(client_metrics)
            
            # 收集客户端用户表示用于知识蒸馏
            client_model = self.client_models[client_id]
            val_loader = self.client_val_loaders[client_id]
            
            client_model.eval()
            with torch.no_grad():
                batch_representations = []
                for batch in val_loader:
                    batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                            for k, v in batch.items()}
                    outputs = client_model(batch, mode='client_only')
                    batch_representations.append(outputs['user_representations'])
                
                if batch_representations:
                    client_repr = torch.cat(batch_representations, dim=0)
                    client_representations.append(client_repr)
        
        # 2. 服务器知识蒸馏
        if client_representations:
            logger.info("Performing server knowledge distillation")
            server_metrics = self.server_knowledge_distillation(client_representations)
            round_metrics['server_metrics'] = server_metrics
        
        # 3. 联邦聚合
        logger.info("Performing federated aggregation")
        aggregation_stats = self.federated_aggregation()
        round_metrics['aggregation_stats'] = aggregation_stats
        
        # 4. 全局评估
        logger.info("Evaluating global model")
        global_metrics = self.evaluate_global_model()
        round_metrics['global_metrics'] = global_metrics
        
        # 5. 记录指标
        self._log_metrics(round_metrics)
        
        # 6. 保存检查点
        if (self.current_round + 1) % self.config['training'].get('save_every', 5) == 0:
            self._save_checkpoint(round_metrics)
        
        self.current_round += 1
        
        logger.info(f"Round {self.current_round} completed. "
                   f"Global NDCG@10: {global_metrics.get('ndcg@10', 0.0):.4f}")
        
        return round_metrics
    
    def train(self):
        """主训练循环"""
        logger.info("Starting federated training")
        
        training_history = []
        
        for round_idx in range(self.fed_rounds):
            try:
                round_metrics = self.train_one_round()
                training_history.append(round_metrics)
                
                # 早停检查
                current_performance = round_metrics['global_metrics'].get('ndcg@10', 0.0)
                if current_performance > self.best_performance:
                    self.best_performance = current_performance
                    self.patience_counter = 0
                    
                    # 保存最佳模型
                    self._save_best_model(round_metrics)
                else:
                    self.patience_counter += 1
                
                # 早停
                patience = self.config['training'].get('patience', 5)
                if self.patience_counter >= patience:
                    logger.info(f"Early stopping triggered after {patience} rounds without improvement")
                    break
                    
            except Exception as e:
                logger.error(f"Error in round {round_idx + 1}: {str(e)}")
                raise e
        
        logger.info("Federated training completed")
        
        # 最终评估
        final_metrics = self.evaluate_global_model()
        logger.info(f"Final performance - NDCG@10: {final_metrics.get('ndcg@10', 0.0):.4f}, "
                   f"Hit Rate@10: {final_metrics.get('hit_rate@10', 0.0):.4f}")
        
        return training_history
    
    def _log_metrics(self, metrics: Dict[str, Any]):
        """记录训练指标"""
        round_num = metrics['round']
        
        # 计算平均客户端指标
        if metrics['client_metrics']:
            avg_client_loss = np.mean([m['train_loss'] for m in metrics['client_metrics']])
            avg_client_ndcg = np.mean([m['val_ndcg'] for m in metrics['client_metrics']])
            
            # TensorBoard记录
            if hasattr(self, 'tb_writer'):
                self.tb_writer.add_scalar('Client/AvgTrainLoss', avg_client_loss, round_num)
                self.tb_writer.add_scalar('Client/AvgValNDCG', avg_client_ndcg, round_num)
            
            # Wandb记录
            if self.config['logging'].get('use_wandb', False):
                wandb.log({
                    'round': round_num,
                    'client_avg_train_loss': avg_client_loss,
                    'client_avg_val_ndcg': avg_client_ndcg,
                    **metrics['global_metrics'],
                    **metrics['aggregation_stats']
                })
    
    def _save_checkpoint(self, metrics: Dict[str, Any]):
        """保存检查点"""
        checkpoint_dir = os.path.join(self.config['logging']['model_dir'], 'checkpoints')
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(checkpoint_dir, f'round_{self.current_round}.pt')
        
        checkpoint = {
            'round': self.current_round,
            'global_model_state': self.global_model.state_dict(),
            'client_model_states': [model.state_dict() for model in self.client_models],
            'server_optimizer_state': self.server_optimizer.state_dict(),
            'client_optimizer_states': [opt.state_dict() for opt in self.client_optimizers],
            'metrics': metrics,
            'config': self.config
        }
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved to {checkpoint_path}")
    
    def _save_best_model(self, metrics: Dict[str, Any]):
        """保存最佳模型"""
        best_model_dir = os.path.join(self.config['logging']['model_dir'], 'best')
        os.makedirs(best_model_dir, exist_ok=True)
        
        # 保存全局模型
        global_model_path = os.path.join(best_model_dir, 'global_model.pt')
        torch.save(self.global_model.state_dict(), global_model_path)
        
        # 保存客户端模型
        for i, client_model in enumerate(self.client_models):
            client_model_path = os.path.join(best_model_dir, f'client_{i}_model.pt')
            torch.save(client_model.state_dict(), client_model_path)
        
        # 保存指标
        metrics_path = os.path.join(best_model_dir, 'metrics.yaml')
        with open(metrics_path, 'w') as f:
            yaml.dump(metrics, f)
        
        logger.info(f"Best model saved to {best_model_dir}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='FedLLM-SRec Collaborative Training')
    parser.add_argument('--config', type=str, default='config/federated_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='Device to use for training')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # 覆盖设备配置
    config['device'] = args.device
    
    # 创建训练器并开始训练
    trainer = FederatedTrainer(config)
    training_history = trainer.train()
    
    print("Training completed successfully!")


if __name__ == '__main__':
    main()
